# AGV统计分析功能说明

## 🎯 功能概述

新增的AGV统计分析功能基于EventLog表中的SourceID字段，提供全面的AGV运行数据分析。SourceID代表AGV小车的编号，通过统计分析可以了解各台AGV的使用频率、运行状况和工作负载分布。

## 🚀 主要功能

### 📊 **数据统计分析**
- **运行次数统计**：统计每台AGV的运行次数
- **使用占比分析**：计算各AGV的使用占比
- **排行榜展示**：按运行次数排序显示AGV排名
- **时间范围分析**：显示数据的时间跨度

### 📈 **可视化图表**
- **柱状图排行榜**：直观显示AGV运行次数排名（前10名）
- **饼图占比分析**：展示各AGV的使用占比分布（前5名+其他）
- **响应式设计**：支持不同屏幕尺寸的设备

### 📋 **详细数据表格**
- **完整排名列表**：显示所有AGV的详细统计数据
- **状态标识**：根据使用频率标记AGV状态（高频/中频/低频）
- **排名徽章**：为前三名AGV添加特殊徽章标识

## 🌟 功能特性

### 🎨 **用户界面**
- **现代化设计**：使用Bootstrap和Chart.js构建美观界面
- **响应式布局**：适配桌面、平板和手机设备
- **交互式图表**：支持鼠标悬停查看详细数据
- **实时刷新**：支持一键刷新最新数据

### 📊 **统计指标**
- **活跃AGV数量**：参与运行的AGV总数
- **总事件数量**：所有AGV产生的事件总数
- **数据时间范围**：统计数据的起止时间
- **最活跃AGV**：运行次数最多的AGV

### 🏆 **排行榜系统**
- **金银铜牌**：前三名AGV获得特殊徽章
- **频率分级**：
  - 🟢 高频：占比 > 20%
  - 🟡 中频：占比 10-20%
  - ⚪ 低频：占比 < 10%

## 📖 使用指南

### 🔗 **访问入口**

#### 方式1：首页快捷入口
1. 访问首页 `http://localhost:5000`
2. 在"数据库信息"区域点击"AGV统计分析"按钮
3. 或在"AGV数据分析"卡片中点击"进入分析页面"

#### 方式2：直接访问
- 直接访问：`http://localhost:5000/agv-analytics`

### 📊 **页面功能**

#### 统计概览区域
```
┌─────────────────────────────────────────────────────────┐
│ [🤖] 活跃AGV数量  [📊] 总事件数量  [📅] 数据时间范围  [👑] 最活跃AGV │
│    5台              1,234次        2012-2025         AGV 2    │
└─────────────────────────────────────────────────────────┘
```

#### 图表展示区域
- **左侧柱状图**：AGV运行次数排行榜（前10名）
- **右侧饼图**：AGV运行占比分布（前5名+其他）

#### 详细数据表格
| 排名 | AGV编号 | 运行次数 | 占比 | 状态 |
|------|---------|----------|------|------|
| 🥇   | AGV 2   | 3,456    | 28.5% | 高频 |
| 🥈   | AGV 5   | 2,789    | 23.1% | 高频 |
| 🥉   | AGV 1   | 1,234    | 10.2% | 中频 |

### 🔄 **操作功能**
- **刷新数据**：点击右上角"刷新数据"按钮
- **图表交互**：鼠标悬停查看具体数值
- **响应式查看**：支持移动设备浏览

## 🔧 技术实现

### 后端API
```python
@app.route('/api/agv-statistics')
def api_agv_statistics():
    """AGV统计数据API"""
    stats = db_manager.get_agv_statistics()
    return jsonify({
        'success': True,
        'data': stats
    })
```

### 数据库查询
```sql
SELECT SourceID, COUNT(*) as count
FROM [EventLog]
WHERE SourceID IS NOT NULL AND SourceID > 0
GROUP BY SourceID
ORDER BY count DESC
```

### 前端图表
- **Chart.js**：用于创建柱状图和饼图
- **Bootstrap**：响应式界面框架
- **JavaScript**：数据处理和交互逻辑

## 📈 数据分析价值

### 🎯 **运营洞察**
- **设备利用率**：了解各AGV的使用频率
- **负载均衡**：识别过载或闲置的AGV
- **维护计划**：根据使用频率制定维护计划
- **效率优化**：优化AGV调度和任务分配

### 📊 **决策支持**
- **设备采购**：基于使用数据决定是否需要增加AGV
- **故障预测**：高频使用的AGV可能需要更多关注
- **成本分析**：了解各AGV的投资回报率
- **性能评估**：评估AGV系统的整体性能

## 🔍 数据示例

### 典型统计结果
```json
{
  "agv_ranking": [
    {"agv_id": "AGV 2", "count": 3456, "percentage": 28.5},
    {"agv_id": "AGV 5", "count": 2789, "percentage": 23.1},
    {"agv_id": "AGV 1", "count": 1234, "percentage": 10.2}
  ],
  "total_events": 12134,
  "total_agvs": 5,
  "time_range": {
    "start": "2012-06-14 22:04:07",
    "end": "2025-05-27 21:03:53"
  }
}
```

## 🚀 未来扩展

### 可能的增强功能
- 📅 **时间段分析**：按日/周/月统计AGV使用情况
- 🔄 **实时监控**：实时更新AGV运行状态
- 📊 **趋势分析**：显示AGV使用趋势图
- 🚨 **异常检测**：识别异常的AGV运行模式
- 📈 **性能指标**：添加更多性能分析指标
- 📋 **报告导出**：支持PDF/Excel格式报告导出

## 🎯 使用建议

### 📊 **日常监控**
- 定期查看AGV使用排行榜
- 关注使用频率异常的AGV
- 监控总体运行趋势

### 🔧 **维护管理**
- 优先维护高频使用的AGV
- 调查低频AGV的原因
- 平衡各AGV的工作负载

### 📈 **优化决策**
- 基于数据优化AGV调度策略
- 识别系统瓶颈和改进机会
- 制定基于数据的运营策略

---

💡 **总结**：AGV统计分析功能为您提供了全面的AGV运行数据洞察，帮助优化设备使用、提高运营效率，是AGV系统管理的重要工具！
