# EventCode统计分析功能实现记录

## 🎯 功能需求

用户要求在AGV运行统计分析页面内添加一个新的div，展示统计EventCode的不同值出现的次数和占比，使用类似AGV详细统计数据的表格样式。

## 🔧 实现方案

### 📱 **前端界面设计**

#### 🎨 **HTML结构**
```html
<!-- EventCode统计分析 -->
<div class="col-12 mb-4">
    <div class="card shadow-sm">
        <div class="card-header bg-warning text-dark">
            <h6 class="mb-0">
                <i class="fas fa-code me-2"></i>
                EventCode统计分析
            </h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover table-sm" id="eventCodeStatsTable">
                    <thead class="table-dark">
                        <tr>
                            <th>排名</th>
                            <th>EventCode</th>
                            <th>出现次数</th>
                            <th>占比</th>
                            <th>频率</th>
                        </tr>
                    </thead>
                    <tbody id="eventCodeStatsTableBody">
                        <!-- 数据将通过JavaScript填充 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
```

#### 🎯 **设计特点**
- **卡片样式**：使用Bootstrap卡片组件，与其他模块保持一致
- **警告色主题**：使用`bg-warning`背景色，突出显示EventCode分析
- **代码图标**：使用`fas fa-code`图标，符合EventCode的含义
- **响应式表格**：使用`table-responsive`确保在小屏幕上可滚动
- **表格样式**：条纹表格、悬停效果、紧凑尺寸

### 🗄️ **后端数据处理**

#### 🔌 **API增强**
```python
# 获取EventCode统计
event_code_query = f"""
    SELECT EventCode, COUNT(*) as event_count
    FROM [EventLog]
    WHERE SourceID IS NOT NULL AND SourceID > 0{time_condition}
    GROUP BY EventCode
    ORDER BY COUNT(*) DESC
"""
cursor.execute(event_code_query, params)
event_code_stats = cursor.fetchall()
```

#### 📊 **数据结构**
```python
return {
    'agv_ranking': agv_data,
    'total_events': total_events,
    'total_agvs': len(agv_data),
    'time_range': time_range_info,
    'event_types': [{'type': row[0], 'count': row[1]} for row in event_type_stats],
    'event_codes': [{'code': row[0], 'count': row[1]} for row in event_code_stats]  # 新增
}
```

### 🎨 **前端数据处理**

#### 📊 **JavaScript函数**
```javascript
// 填充EventCode统计表格
function fillEventCodeTable(eventCodeData) {
    const tbody = document.getElementById('eventCodeStatsTableBody');
    tbody.innerHTML = '';
    
    // 计算总数用于百分比计算
    const totalCount = eventCodeData.reduce((sum, item) => sum + item.count, 0);
    
    eventCodeData.forEach((item, index) => {
        const row = document.createElement('tr');
        
        // 计算百分比
        const percentage = ((item.count / totalCount) * 100).toFixed(2);
        
        // 排名徽章
        let rankBadge = '';
        if (index === 0) rankBadge = '<span class="badge bg-warning">🥇</span>';
        else if (index === 1) rankBadge = '<span class="badge bg-secondary">🥈</span>';
        else if (index === 2) rankBadge = '<span class="badge bg-warning">🥉</span>';
        else rankBadge = `<span class="badge bg-light text-dark">${index + 1}</span>`;
        
        // 频率标识
        let frequencyBadge = '';
        if (percentage > 20) frequencyBadge = '<span class="badge bg-danger">极高频</span>';
        else if (percentage > 10) frequencyBadge = '<span class="badge bg-warning">高频</span>';
        else if (percentage > 5) frequencyBadge = '<span class="badge bg-info">中频</span>';
        else if (percentage > 1) frequencyBadge = '<span class="badge bg-success">低频</span>';
        else frequencyBadge = '<span class="badge bg-secondary">极低频</span>';
        
        // EventCode显示（处理null值）
        const eventCodeDisplay = item.code || item.code === 0 ? item.code : '<span class="text-muted">未知</span>';
        
        row.innerHTML = `
            <td>${rankBadge}</td>
            <td><strong>${eventCodeDisplay}</strong></td>
            <td>${item.count.toLocaleString()}</td>
            <td>${percentage}%</td>
            <td>${frequencyBadge}</td>
        `;
        
        tbody.appendChild(row);
    });
}
```

#### 🔄 **数据流集成**
```javascript
// 显示统计数据
function displayStatistics(data) {
    // 更新概览数据
    updateOverview(data);

    // 创建图表
    createRankingChart(data.agv_ranking);

    // 填充EventCode统计表格 (新增)
    fillEventCodeTable(data.event_codes);

    // 填充详细表格
    fillDetailTable(data.agv_ranking);

    // 显示所有内容
    document.getElementById('statisticsOverview').style.display = 'block';
    document.getElementById('chartsAndDataContainer').style.display = 'block';
}
```

## 🌟 功能特性

### ✅ **核心功能**
1. **EventCode统计**：显示所有EventCode值的出现次数
2. **占比计算**：自动计算每个EventCode的百分比占比
3. **排名显示**：按出现次数降序排列，显示排名徽章
4. **频率分级**：根据占比自动分级（极高频、高频、中频、低频、极低频）
5. **时间范围联动**：与时间选择器联动，支持时间范围过滤

### 🚀 **用户体验**
1. **直观显示**：表格形式清晰展示EventCode分布情况
2. **视觉层次**：使用徽章和颜色区分不同等级的EventCode
3. **数据完整**：显示次数、占比、频率等多维度信息
4. **响应式设计**：在各种设备上都有良好的显示效果
5. **一致性**：与现有AGV详细统计数据表格样式保持一致

### 📊 **数据分析价值**
1. **事件分布分析**：了解系统中各种事件的发生频率
2. **异常检测**：识别异常高频或低频的EventCode
3. **系统监控**：监控系统事件的变化趋势
4. **故障诊断**：通过EventCode分布辅助故障分析

## 🎯 技术实现细节

### 🗄️ **数据库查询优化**
```sql
-- 支持时间范围过滤的EventCode统计查询
SELECT EventCode, COUNT(*) as event_count
FROM [EventLog]
WHERE SourceID IS NOT NULL AND SourceID > 0
  AND EventTime >= ? AND EventTime <= ?  -- 可选的时间条件
GROUP BY EventCode
ORDER BY COUNT(*) DESC
```

### 🎨 **样式设计**
- **卡片头部**：使用警告色（`bg-warning`）突出显示
- **表格样式**：条纹表格、悬停效果、紧凑尺寸
- **徽章系统**：排名徽章（🥇🥈🥉）和频率徽章
- **颜色编码**：不同频率等级使用不同颜色的徽章

### 📱 **响应式适配**
- **表格响应式**：使用`table-responsive`类
- **移动端优化**：在小屏幕上支持水平滚动
- **布局适配**：与现有模块保持一致的间距和布局

## 🔄 数据流程

### 📥 **完整数据流**
```
用户访问页面 → 加载统计数据 → 后端查询EventCode → 
计算统计信息 → 返回JSON数据 → 前端解析 → 
填充表格 → 显示给用户
```

### 📊 **数据处理步骤**
1. **数据库查询**：按EventCode分组统计出现次数
2. **排序处理**：按出现次数降序排列
3. **百分比计算**：前端计算每个EventCode的占比
4. **频率分级**：根据占比自动分配频率等级
5. **表格渲染**：动态生成HTML表格行

## 📋 功能验证

### ✅ **显示效果验证**
- [x] EventCode统计表格正确显示
- [x] 排名徽章正确显示（🥇🥈🥉）
- [x] 出现次数格式化显示（千分位分隔符）
- [x] 百分比计算准确
- [x] 频率徽章颜色正确

### ✅ **功能验证**
- [x] 时间范围过滤正常工作
- [x] 数据排序正确（按次数降序）
- [x] null值处理正确（显示为"未知"）
- [x] 响应式布局正常
- [x] 与其他模块样式一致

### ✅ **性能验证**
- [x] 数据库查询效率良好
- [x] 前端渲染速度快
- [x] 大数据量下表现稳定
- [x] 内存使用合理

## 🎨 视觉效果

### 📊 **表格布局**
```
排名    EventCode    出现次数      占比      频率
🥇      1001        15,234      25.67%    极高频
🥈      2002        12,456      21.03%    极高频
🥉      3003        8,789       14.83%    高频
4       4004        5,432       9.17%     中频
5       5005        3,210       5.42%     中频
...
```

### 🎯 **频率分级标准**
- **极高频** (>20%): 红色徽章 `bg-danger`
- **高频** (10-20%): 警告色徽章 `bg-warning`
- **中频** (5-10%): 信息色徽章 `bg-info`
- **低频** (1-5%): 成功色徽章 `bg-success`
- **极低频** (<1%): 次要色徽章 `bg-secondary`

## 🚀 部署状态

### ✅ **已完成功能**
- [x] 后端API增强（EventCode查询）
- [x] 前端HTML结构添加
- [x] JavaScript数据处理函数
- [x] 表格样式和布局
- [x] 排名和频率徽章系统
- [x] 时间范围联动功能
- [x] 响应式设计适配

### 📊 **测试验证**
- [x] API返回EventCode数据正常
- [x] 前端表格渲染正确
- [x] 时间过滤功能正常
- [x] 排序和计算准确
- [x] 样式显示美观
- [x] 响应式效果良好

---

💡 **总结**：成功在AGV运行统计分析页面添加了EventCode统计分析功能，提供了完整的EventCode分布分析，包括出现次数、占比、排名和频率分级，与现有功能完美集成，为用户提供了更全面的系统事件分析能力！
