# ACCESS数据库查询程序

这个项目包含了用Python连接和查询ACCESS数据库的程序。

## 文件说明

- `EVT_LOG.MDB` - ACCESS数据库文件
- `access_db_query.py` - 完整功能的数据库查询程序
- `simple_query.py` - 简化版的查询程序
- `requirements.txt` - Python依赖包列表

## 环境要求

1. Python 3.x
2. pyodbc库
3. Microsoft Access Driver (通常Windows系统自带)

## 安装依赖

```bash
pip install pyodbc
```

或者使用requirements.txt：

```bash
pip install -r requirements.txt
```

## 使用方法

### Web版本（推荐）

1. 启动Web应用：
```bash
python app.py
```

2. 在浏览器中访问：`http://localhost:5000`

Web版本功能：
- 🌐 现代化的Web界面
- 📊 表格数据可视化显示
- 🔍 快速查询和搜索功能
- 📱 响应式设计，支持移动设备
- 💾 数据导出功能
- 📋 表结构信息查看
- 🎨 美观的用户界面

### 命令行版本

#### 方法1：运行完整版程序
```bash
python access_db_query.py
```

这个程序会：
- 连接到EVT_LOG.MDB数据库
- 显示所有表名
- 查询每个表的前7条记录
- 格式化显示结果

#### 方法2：运行简化版程序
```bash
python simple_query.py
```

这个程序会：
- 直接连接数据库并查询前7条记录
- 以简洁的格式显示结果

## 数据库结构

根据查询结果，数据库包含以下表：

### EventLog表
包含AGV系统的事件日志，主要字段：
- Counter: 计数器
- EventType: 事件类型
- EventCode: 事件代码
- SourceType: 源类型
- SourceID: 源ID
- EventTime: 事件时间
- EventString: 事件描述

### Revision表
包含版本信息：
- Revision: 版本号

## 查询结果示例

程序成功查询到EventLog表的7条记录，包含AGV装卸货操作的详细日志信息，以及Revision表的1条版本记录。

## 🆕 字段控制功能

### 功能特点
- **灵活控制**：可以单独显示/隐藏任何数据字段
- **批量操作**：支持一键显示全部或隐藏全部字段
- **状态记忆**：自动保存您的字段显示偏好设置
- **实时切换**：无需刷新页面即可切换字段显示状态

### 使用方法
1. 在表详情页面点击"字段控制"按钮
2. 勾选/取消勾选要显示/隐藏的字段
3. 使用"全部显示"/"全部隐藏"进行批量操作
4. 设置会自动保存，下次访问时自动恢复

详细使用说明请参考：[字段控制功能说明.md](字段控制功能说明.md)

### EventString字段优化
- **智能显示**：长文本字段（如EventString）会智能截断显示
- **完整查看**：点击🔍按钮在弹窗中查看完整内容
- **格式保持**：保持原始文本格式，支持换行和特殊字符
- **一键复制**：可以复制完整内容到剪贴板
- **多场景支持**：在表详情页面和首页快速查询中都可使用

详细说明请参考：[EventString字段优化说明.md](EventString字段优化说明.md)

### AGV统计分析功能
- **数据来源**：基于EventLog表中的SourceID字段进行统计分析
- **分析内容**：AGV运行次数、使用占比、排行榜等
- **可视化图表**：柱状图排行榜、饼图占比分析
- **访问方式**：首页点击"AGV统计分析"按钮或直接访问`/agv-analytics`
- **功能特色**：
  - 🏆 AGV运行次数排行榜（前10名）
  - 🥧 AGV使用占比饼图（前5名+其他）
  - 📊 详细统计数据表格
  - 📈 统计概览（活跃AGV数量、总事件数等）
  - 🔄 实时数据刷新功能

详细说明请参考：[AGV统计分析功能说明.md](AGV统计分析功能说明.md)

## 注意事项

1. 确保ACCESS数据库文件路径正确
2. 需要安装Microsoft Access Driver
3. 如果遇到编码问题，可能需要调整字符编码设置
4. 程序会自动处理数据库连接的打开和关闭

## 故障排除

如果遇到连接问题：
1. 检查数据库文件是否存在
2. 确认已安装Microsoft Access Driver
3. 检查文件权限
4. 确认pyodbc库已正确安装
