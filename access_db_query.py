#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ACCESS数据库查询程序
连接到EVT_LOG.MDB数据库并查询前7条数据
"""

import pyodbc
import os
import sys
from pathlib import Path

def connect_to_access_db(db_path):
    """
    连接到ACCESS数据库
    
    Args:
        db_path (str): 数据库文件路径
        
    Returns:
        pyodbc.Connection: 数据库连接对象
    """
    try:
        # 检查数据库文件是否存在
        if not os.path.exists(db_path):
            raise FileNotFoundError(f"数据库文件不存在: {db_path}")
        
        # 构建连接字符串
        # 使用Microsoft Access Driver
        conn_str = (
            r'DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};'
            f'DBQ={db_path};'
        )
        
        # 建立连接
        connection = pyodbc.connect(conn_str)
        print(f"成功连接到数据库: {db_path}")
        return connection
        
    except pyodbc.Error as e:
        print(f"连接数据库时发生错误: {e}")
        return None
    except Exception as e:
        print(f"发生未知错误: {e}")
        return None

def get_table_names(connection):
    """
    获取数据库中所有表的名称
    
    Args:
        connection: 数据库连接对象
        
    Returns:
        list: 表名列表
    """
    try:
        cursor = connection.cursor()
        tables = []
        for table_info in cursor.tables(tableType='TABLE'):
            tables.append(table_info.table_name)
        return tables
    except Exception as e:
        print(f"获取表名时发生错误: {e}")
        return []

def query_first_7_records(connection, table_name):
    """
    查询指定表的前7条记录
    
    Args:
        connection: 数据库连接对象
        table_name (str): 表名
        
    Returns:
        list: 查询结果
    """
    try:
        cursor = connection.cursor()
        
        # 查询前7条记录
        query = f"SELECT TOP 7 * FROM [{table_name}]"
        cursor.execute(query)
        
        # 获取列名
        columns = [column[0] for column in cursor.description]
        
        # 获取数据
        rows = cursor.fetchall()
        
        return columns, rows
        
    except Exception as e:
        print(f"查询数据时发生错误: {e}")
        return None, None

def display_results(columns, rows, table_name):
    """
    显示查询结果
    
    Args:
        columns (list): 列名列表
        rows (list): 数据行列表
        table_name (str): 表名
    """
    if not columns or not rows:
        print("没有查询到数据")
        return
    
    print(f"\n=== 表 '{table_name}' 的前7条记录 ===")
    print(f"总共找到 {len(rows)} 条记录")
    print("-" * 80)
    
    # 打印列标题
    header = " | ".join(f"{col:15}" for col in columns)
    print(header)
    print("-" * len(header))
    
    # 打印数据行
    for i, row in enumerate(rows, 1):
        row_str = " | ".join(f"{str(val):15}" for val in row)
        print(f"{i:2d}. {row_str}")
    
    print("-" * 80)

def main():
    """
    主函数
    """
    # 数据库文件路径
    db_path = Path(__file__).parent / "EVT_LOG.MDB"
    db_path = str(db_path.resolve())
    
    print("ACCESS数据库查询程序")
    print("=" * 50)
    print(f"数据库路径: {db_path}")
    
    # 连接数据库
    connection = connect_to_access_db(db_path)
    if not connection:
        print("无法连接到数据库，程序退出")
        return
    
    try:
        # 获取所有表名
        tables = get_table_names(connection)
        if not tables:
            print("数据库中没有找到表")
            return
        
        print(f"\n数据库中找到 {len(tables)} 个表:")
        for i, table in enumerate(tables, 1):
            print(f"  {i}. {table}")
        
        # 查询每个表的前7条记录
        for table_name in tables:
            print(f"\n正在查询表: {table_name}")
            columns, rows = query_first_7_records(connection, table_name)
            display_results(columns, rows, table_name)
            
            # 如果有多个表，询问是否继续
            if len(tables) > 1 and table_name != tables[-1]:
                user_input = input("\n按Enter继续查询下一个表，输入'q'退出: ")
                if user_input.lower() == 'q':
                    break
    
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
    
    finally:
        # 关闭数据库连接
        if connection:
            connection.close()
            print("\n数据库连接已关闭")

if __name__ == "__main__":
    main()
