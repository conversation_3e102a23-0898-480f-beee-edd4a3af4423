#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Flask Web应用 - ACCESS数据库查询
浏览器版本的数据库查询工具
"""

from flask import Flask, render_template, jsonify, request
import pyodbc
import os
from pathlib import Path
import traceback
from datetime import datetime

app = Flask(__name__)

# 数据库配置
DB_PATH = Path(__file__).parent / "EVT_LOG.MDB"

class DatabaseManager:
    """数据库管理类"""

    def __init__(self, db_path):
        self.db_path = str(db_path.resolve())
        
    def get_connection(self):
        """获取数据库连接"""
        try:
            conn_str = (
                r'DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};'
                f'DBQ={self.db_path};'
            )
            return pyodbc.connect(conn_str)
        except Exception as e:
            raise Exception(f"数据库连接失败: {str(e)}")
    
    def get_tables(self):
        """获取所有表名"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                tables = [table_info.table_name for table_info in cursor.tables(tableType='TABLE')]
                return tables
        except Exception as e:
            raise Exception(f"获取表名失败: {str(e)}")
    
    def query_table_data(self, table_name, limit=7):
        """查询表数据"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 查询数据
                query = f"SELECT TOP {limit} * FROM [{table_name}]"
                cursor.execute(query)
                
                # 获取列名
                columns = [column[0] for column in cursor.description]
                
                # 获取数据
                rows = cursor.fetchall()
                
                # 转换为字典列表
                data = []
                for row in rows:
                    row_dict = {}
                    for i, value in enumerate(row):
                        # 处理日期时间格式
                        if isinstance(value, datetime):
                            row_dict[columns[i]] = value.strftime('%Y-%m-%d %H:%M:%S')
                        else:
                            row_dict[columns[i]] = str(value) if value is not None else ''
                    data.append(row_dict)
                
                return {
                    'columns': columns,
                    'data': data,
                    'total_rows': len(data)
                }
        except Exception as e:
            raise Exception(f"查询表 {table_name} 失败: {str(e)}")
    
    def get_table_info(self, table_name):
        """获取表的详细信息"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 获取表结构信息
                cursor.execute(f"SELECT TOP 1 * FROM [{table_name}]")
                columns_info = []
                for column in cursor.description:
                    columns_info.append({
                        'name': column[0],
                        'type': str(column[1]),
                        'size': column[3] if column[3] else 'N/A'
                    })

                # 获取总记录数
                cursor.execute(f"SELECT COUNT(*) FROM [{table_name}]")
                total_count = cursor.fetchone()[0]

                return {
                    'columns_info': columns_info,
                    'total_count': total_count
                }
        except Exception as e:
            raise Exception(f"获取表信息失败: {str(e)}")

    def get_agv_statistics(self, start_date=None, end_date=None):
        """获取AGV统计数据"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 首先检查表是否存在以及数据结构
                try:
                    cursor.execute("SELECT TOP 1 * FROM [EventLog]")
                    columns = [column[0] for column in cursor.description]
                    print(f"EventLog表的字段: {columns}")
                except Exception as e:
                    print(f"EventLog表访问错误: {e}")
                    raise Exception("EventLog表不存在或无法访问")

                # 构建时间条件
                time_condition = ""
                params = []
                if start_date and end_date:
                    time_condition = " AND EventTime >= ? AND EventTime <= ?"
                    # 将日期转换为包含时间的格式
                    params = [f"{start_date} 00:00:00", f"{end_date} 23:59:59"]

                # 统计SourceID的分布
                query = f"""
                    SELECT SourceID, COUNT(*) as event_count
                    FROM [EventLog]
                    WHERE SourceID IS NOT NULL AND SourceID > 0{time_condition}
                    GROUP BY SourceID
                    ORDER BY COUNT(*) DESC
                """

                cursor.execute(query, params)
                source_stats = cursor.fetchall()

                # 计算总数和百分比
                total_events = sum(row[1] for row in source_stats)

                agv_data = []
                for source_id, count in source_stats:
                    percentage = (count / total_events * 100) if total_events > 0 else 0
                    agv_data.append({
                        'agv_id': f"AGV {source_id}",
                        'source_id': source_id,
                        'count': count,
                        'percentage': round(percentage, 2)
                    })

                # 获取时间范围
                time_query = f"""
                    SELECT MIN(EventTime) as min_time, MAX(EventTime) as max_time, COUNT(*) as total_count
                    FROM [EventLog]
                    WHERE SourceID IS NOT NULL AND SourceID > 0{time_condition}
                """
                cursor.execute(time_query, params)
                time_info = cursor.fetchone()

                # 获取事件类型统计
                event_type_query = f"""
                    SELECT EventType, COUNT(*) as event_count
                    FROM [EventLog]
                    WHERE SourceID IS NOT NULL AND SourceID > 0{time_condition}
                    GROUP BY EventType
                    ORDER BY COUNT(*) DESC
                """
                cursor.execute(event_type_query, params)
                event_type_stats = cursor.fetchall()

                # 获取EventCode统计（包含所有记录，不限制SourceID）
                event_code_query = f"""
                    SELECT EventCode, COUNT(*) as event_count
                    FROM [EventLog]
                    WHERE EventCode IS NOT NULL{time_condition}
                    GROUP BY EventCode
                    ORDER BY COUNT(*) DESC
                """
                cursor.execute(event_code_query, params)
                event_code_stats = cursor.fetchall()

                # 构建时间范围信息
                time_range_info = {}
                if start_date and end_date:
                    # 如果用户指定了时间范围，返回用户指定的范围
                    time_range_info = {
                        'start': f"{start_date} 00:00:00",
                        'end': f"{end_date} 23:59:59",
                        'total_records': time_info[2],
                        'user_specified': True
                    }
                else:
                    # 如果没有指定时间范围，返回数据库中的实际范围
                    time_range_info = {
                        'start': time_info[0].strftime('%Y-%m-%d %H:%M:%S') if time_info[0] else None,
                        'end': time_info[1].strftime('%Y-%m-%d %H:%M:%S') if time_info[1] else None,
                        'total_records': time_info[2],
                        'user_specified': False
                    }

                return {
                    'agv_ranking': agv_data,
                    'total_events': total_events,
                    'total_agvs': len(agv_data),
                    'time_range': time_range_info,
                    'event_types': [{'type': row[0], 'count': row[1]} for row in event_type_stats],
                    'event_codes': [{'code': row[0], 'count': row[1]} for row in event_code_stats]
                }
        except Exception as e:
            raise Exception(f"获取AGV统计数据失败: {str(e)}")

# 创建数据库管理器实例
db_manager = DatabaseManager(DB_PATH)

@app.route('/')
def index():
    """主页"""
    try:
        tables = db_manager.get_tables()
        return render_template('index.html', tables=tables, db_path=str(DB_PATH))
    except Exception as e:
        return render_template('error.html', error=str(e))

@app.route('/api/tables')
def api_tables():
    """获取所有表名的API"""
    try:
        tables = db_manager.get_tables()
        return jsonify({
            'success': True,
            'tables': tables
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/table/<table_name>')
def api_table_data(table_name):
    """获取表数据的API"""
    try:
        limit = request.args.get('limit', 7, type=int)
        data = db_manager.query_table_data(table_name, limit)
        table_info = db_manager.get_table_info(table_name)
        
        return jsonify({
            'success': True,
            'table_name': table_name,
            'data': data,
            'info': table_info
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/table/<table_name>')
def view_table(table_name):
    """查看表数据页面"""
    try:
        limit = request.args.get('limit', 7, type=int)
        data = db_manager.query_table_data(table_name, limit)
        table_info = db_manager.get_table_info(table_name)

        return render_template('table.html',
                             table_name=table_name,
                             data=data,
                             info=table_info,
                             limit=limit)
    except Exception as e:
        return render_template('error.html', error=str(e))

@app.route('/agv-analytics')
def agv_analytics():
    """AGV统计分析页面"""
    try:
        return render_template('agv_analytics.html')
    except Exception as e:
        return render_template('error.html', error=str(e))

@app.route('/api/agv-statistics')
def api_agv_statistics():
    """AGV统计数据API"""
    try:
        # 获取时间范围参数
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')

        stats = db_manager.get_agv_statistics(start_date, end_date)
        return jsonify({
            'success': True,
            'data': stats
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.errorhandler(404)
def not_found(error):
    return render_template('error.html', error="页面未找到"), 404

@app.errorhandler(500)
def internal_error(error):
    return render_template('error.html', error="服务器内部错误"), 500

if __name__ == '__main__':
    # 检查数据库文件是否存在
    if not DB_PATH.exists():
        print(f"错误: 数据库文件不存在: {DB_PATH}")
        exit(1)
    
    print(f"数据库路径: {DB_PATH}")
    print("启动Flask应用...")
    print("访问地址: http://localhost:5001")
    
    app.run(debug=True, host='127.0.0.1', port=5001)
