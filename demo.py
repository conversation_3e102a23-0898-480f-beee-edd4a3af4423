#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ACCESS数据库查询工具演示脚本
展示字段控制功能和Web界面特性
"""

import webbrowser
import time
import subprocess
import sys
import os
from pathlib import Path

def print_banner():
    """打印欢迎横幅"""
    print("=" * 60)
    print("🚀 ACCESS数据库查询工具 - 演示程序")
    print("=" * 60)
    print("✨ 新功能：字段控制 - 自由显示/隐藏任何字段")
    print("🌐 现代化Web界面 - 响应式设计")
    print("📊 数据可视化 - 美观的表格显示")
    print("💾 设置记忆 - 自动保存偏好设置")
    print("=" * 60)

def check_requirements():
    """检查运行环境"""
    print("\n🔍 检查运行环境...")
    
    # 检查Python版本
    python_version = sys.version_info
    if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 6):
        print("❌ 需要Python 3.6或更高版本")
        return False
    print(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # 检查必要的包
    required_packages = ['flask', 'pyodbc']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}包已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package}包未安装")
    
    if missing_packages:
        print(f"\n📦 需要安装以下包: {', '.join(missing_packages)}")
        print("运行命令: pip install " + " ".join(missing_packages))
        return False
    
    # 检查数据库文件
    db_path = Path("EVT_LOG.MDB")
    if db_path.exists():
        print(f"✅ 数据库文件存在: {db_path.resolve()}")
    else:
        print(f"❌ 数据库文件不存在: {db_path.resolve()}")
        return False
    
    return True

def start_demo():
    """启动演示"""
    print("\n🚀 启动Web应用...")
    
    try:
        # 启动Flask应用
        print("正在启动Flask服务器...")
        process = subprocess.Popen([
            sys.executable, "app.py"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # 等待服务器启动
        print("等待服务器启动...")
        time.sleep(3)
        
        # 检查进程是否还在运行
        if process.poll() is None:
            print("✅ Flask服务器启动成功!")
            print("🌐 访问地址: http://localhost:5000")
            
            # 自动打开浏览器
            print("\n🔗 正在打开浏览器...")
            webbrowser.open("http://localhost:5000")
            
            print("\n" + "=" * 60)
            print("🎉 演示已启动！")
            print("=" * 60)
            print("📋 功能演示指南:")
            print("1. 🏠 首页 - 查看数据库信息和表列表")
            print("2. 📊 点击表名 - 查看详细数据")
            print("3. 👁️ 字段控制 - 点击'字段控制'按钮")
            print("4. ✅ 勾选/取消 - 控制字段显示/隐藏")
            print("5. 🔄 批量操作 - 全部显示/全部隐藏")
            print("6. 💾 自动保存 - 设置会自动记忆")
            print("7. 📱 响应式 - 支持移动设备")
            print("=" * 60)
            print("⚠️  按 Ctrl+C 停止服务器")
            print("=" * 60)
            
            try:
                # 等待用户中断
                process.wait()
            except KeyboardInterrupt:
                print("\n\n🛑 正在停止服务器...")
                process.terminate()
                process.wait()
                print("✅ 服务器已停止")
        else:
            # 获取错误信息
            stdout, stderr = process.communicate()
            print("❌ Flask服务器启动失败!")
            if stderr:
                print(f"错误信息: {stderr.decode()}")
            return False
            
    except FileNotFoundError:
        print("❌ 找不到app.py文件")
        return False
    except Exception as e:
        print(f"❌ 启动失败: {str(e)}")
        return False
    
    return True

def show_features():
    """显示功能特性"""
    print("\n🌟 主要功能特性:")
    print("┌─────────────────────────────────────────────────────┐")
    print("│ 🎯 字段控制功能 (NEW!)                              │")
    print("│   • 自由显示/隐藏任何数据字段                        │")
    print("│   • 批量操作：全部显示/隐藏                          │")
    print("│   • 设置自动保存和恢复                              │")
    print("│   • 实时切换，无需刷新页面                          │")
    print("├─────────────────────────────────────────────────────┤")
    print("│ 🌐 现代化Web界面                                    │")
    print("│   • Bootstrap响应式设计                             │")
    print("│   • 美观的用户界面                                  │")
    print("│   • 移动设备友好                                    │")
    print("├─────────────────────────────────────────────────────┤")
    print("│ 📊 数据可视化                                       │")
    print("│   • 表格数据清晰显示                                │")
    print("│   • 行高亮选择功能                                  │")
    print("│   • 数据导出为CSV                                   │")
    print("├─────────────────────────────────────────────────────┤")
    print("│ 🔍 查询功能                                         │")
    print("│   • 快速查询和搜索                                  │")
    print("│   • 可调整查询记录数                                │")
    print("│   • 表结构信息查看                                  │")
    print("└─────────────────────────────────────────────────────┘")

def main():
    """主函数"""
    print_banner()
    show_features()
    
    if not check_requirements():
        print("\n❌ 环境检查失败，请解决上述问题后重试")
        input("\n按Enter键退出...")
        return
    
    print("\n✅ 环境检查通过!")
    
    # 询问是否启动演示
    while True:
        choice = input("\n🚀 是否启动演示? (y/n): ").lower().strip()
        if choice in ['y', 'yes', '是', '']:
            start_demo()
            break
        elif choice in ['n', 'no', '否']:
            print("👋 演示已取消")
            break
        else:
            print("请输入 y 或 n")
    
    print("\n👋 感谢使用ACCESS数据库查询工具!")
    input("按Enter键退出...")

if __name__ == "__main__":
    main()
