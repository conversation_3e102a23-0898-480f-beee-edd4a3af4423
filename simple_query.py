#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的ACCESS数据库查询程序
连接到EVT_LOG.MDB数据库并查询前7条数据
"""

import pyodbc
import os
from pathlib import Path

def main():
    # 数据库文件路径
    db_path = Path(__file__).parent / "EVT_LOG.MDB"
    
    try:
        # 连接字符串
        conn_str = (
            r'DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};'
            f'DBQ={db_path};'
        )
        
        # 连接数据库
        print(f"正在连接数据库: {db_path}")
        connection = pyodbc.connect(conn_str)
        print("连接成功！")
        
        cursor = connection.cursor()
        
        # 获取所有表名
        tables = [table_info.table_name for table_info in cursor.tables(tableType='TABLE')]
        print(f"\n数据库中的表: {', '.join(tables)}")
        
        # 查询每个表的前7条记录
        for table_name in tables:
            print(f"\n{'='*60}")
            print(f"表: {table_name} - 前7条记录")
            print('='*60)
            
            # 执行查询
            cursor.execute(f"SELECT TOP 7 * FROM [{table_name}]")
            
            # 获取列名
            columns = [column[0] for column in cursor.description]
            print("列名:", " | ".join(columns))
            print("-" * 80)
            
            # 获取并显示数据
            rows = cursor.fetchall()
            for i, row in enumerate(rows, 1):
                print(f"第{i}行:", " | ".join(str(val) for val in row))
            
            print(f"\n共查询到 {len(rows)} 条记录")
        
        # 关闭连接
        connection.close()
        print("\n数据库连接已关闭")
        
    except pyodbc.Error as e:
        print(f"数据库错误: {e}")
    except Exception as e:
        print(f"程序错误: {e}")

if __name__ == "__main__":
    main()
