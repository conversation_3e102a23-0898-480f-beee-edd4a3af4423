// JavaScript功能 - ACCESS数据库查询工具

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 初始化工具提示
    initTooltips();
    
    // 初始化表格功能
    initTableFeatures();
    
    // 初始化搜索功能
    initSearchFeatures();
    
    // 显示加载完成消息
    console.log('ACCESS数据库查询工具已加载完成');
});

// 初始化Bootstrap工具提示
function initTooltips() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// 初始化表格功能
function initTableFeatures() {
    // 表格行点击高亮功能
    const tableRows = document.querySelectorAll('#dataTable tbody tr');
    tableRows.forEach(row => {
        row.addEventListener('click', function() {
            // 移除其他行的高亮
            tableRows.forEach(r => r.classList.remove('table-warning'));
            // 添加当前行高亮
            this.classList.add('table-warning');
        });
    });
    
    // 表格列排序功能（简单实现）
    const tableHeaders = document.querySelectorAll('#dataTable th');
    tableHeaders.forEach((header, index) => {
        if (index > 0) { // 跳过序号列
            header.style.cursor = 'pointer';
            header.addEventListener('click', function() {
                sortTable(index);
            });
        }
    });
}

// 表格排序功能
function sortTable(columnIndex) {
    const table = document.getElementById('dataTable');
    if (!table) return;
    
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    
    // 获取当前排序状态
    const header = table.querySelectorAll('th')[columnIndex];
    const isAscending = !header.classList.contains('sort-desc');
    
    // 清除所有排序标记
    table.querySelectorAll('th').forEach(th => {
        th.classList.remove('sort-asc', 'sort-desc');
    });
    
    // 添加当前排序标记
    header.classList.add(isAscending ? 'sort-asc' : 'sort-desc');
    
    // 排序行
    rows.sort((a, b) => {
        const aText = a.cells[columnIndex].textContent.trim();
        const bText = b.cells[columnIndex].textContent.trim();
        
        // 尝试数字比较
        const aNum = parseFloat(aText);
        const bNum = parseFloat(bText);
        
        if (!isNaN(aNum) && !isNaN(bNum)) {
            return isAscending ? aNum - bNum : bNum - aNum;
        }
        
        // 字符串比较
        return isAscending ? 
            aText.localeCompare(bText) : 
            bText.localeCompare(aText);
    });
    
    // 重新插入排序后的行
    rows.forEach(row => tbody.appendChild(row));
    
    // 更新行号
    updateRowNumbers();
}

// 更新行号
function updateRowNumbers() {
    const rows = document.querySelectorAll('#dataTable tbody tr');
    rows.forEach((row, index) => {
        row.cells[0].textContent = index + 1;
    });
}

// 初始化搜索功能
function initSearchFeatures() {
    // 表格内容搜索
    const searchInput = document.getElementById('tableSearch');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            filterTable(this.value);
        });
    }
}

// 表格过滤功能
function filterTable(searchTerm) {
    const table = document.getElementById('dataTable');
    if (!table) return;
    
    const rows = table.querySelectorAll('tbody tr');
    const term = searchTerm.toLowerCase();
    
    rows.forEach(row => {
        const cells = row.querySelectorAll('td');
        let found = false;
        
        cells.forEach(cell => {
            if (cell.textContent.toLowerCase().includes(term)) {
                found = true;
            }
        });
        
        row.style.display = found ? '' : 'none';
    });
    
    // 更新可见行的行号
    updateVisibleRowNumbers();
}

// 更新可见行号
function updateVisibleRowNumbers() {
    const visibleRows = document.querySelectorAll('#dataTable tbody tr:not([style*="display: none"])');
    visibleRows.forEach((row, index) => {
        row.cells[0].textContent = index + 1;
    });
}

// 显示加载状态
function showLoading(element) {
    if (element) {
        element.innerHTML = '<span class="loading"></span> 加载中...';
        element.disabled = true;
    }
}

// 隐藏加载状态
function hideLoading(element, originalText) {
    if (element) {
        element.innerHTML = originalText;
        element.disabled = false;
    }
}

// 显示成功消息
function showSuccess(message) {
    showAlert(message, 'success');
}

// 显示错误消息
function showError(message) {
    showAlert(message, 'danger');
}

// 显示警告消息
function showWarning(message) {
    showAlert(message, 'warning');
}

// 显示信息消息
function showInfo(message) {
    showAlert(message, 'info');
}

// 通用警告框显示函数
function showAlert(message, type = 'info') {
    // 创建警告框元素
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // 插入到页面顶部
    const container = document.querySelector('.container');
    if (container) {
        container.insertBefore(alertDiv, container.firstChild);
        
        // 自动隐藏（除了错误消息）
        if (type !== 'danger') {
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }
    }
}

// 复制文本到剪贴板
function copyToClipboard(text) {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
            showSuccess('已复制到剪贴板');
        }).catch(() => {
            showError('复制失败');
        });
    } else {
        // 降级方案
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        try {
            document.execCommand('copy');
            showSuccess('已复制到剪贴板');
        } catch (err) {
            showError('复制失败');
        }
        document.body.removeChild(textArea);
    }
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 格式化日期时间
function formatDateTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 节流函数
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// 页面可见性检测
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        console.log('页面已隐藏');
    } else {
        console.log('页面已显示');
    }
});

// 错误处理
window.addEventListener('error', function(e) {
    console.error('JavaScript错误:', e.error);
    showError('页面发生错误，请刷新重试');
});

// 网络状态检测
window.addEventListener('online', function() {
    showSuccess('网络连接已恢复');
});

window.addEventListener('offline', function() {
    showWarning('网络连接已断开');
});
