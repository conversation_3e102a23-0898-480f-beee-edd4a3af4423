/* 自定义样式 - ACCESS数据库查询工具 */

/* 全局样式 */
body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: bold;
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: 10px;
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
}

.card-header {
    border-radius: 10px 10px 0 0 !important;
    font-weight: 600;
}

/* 表格卡片特殊样式 */
.table-card {
    cursor: pointer;
    transition: all 0.3s ease;
}

.table-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
}

.table-card .card-body {
    padding: 2rem;
}

.table-card .fa-table {
    color: #007bff;
    margin-bottom: 1rem;
}

/* 按钮样式 */
.btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn-group .btn {
    border-radius: 6px;
}

.btn-group .btn:not(:last-child) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.btn-group .btn:not(:first-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

/* 表格样式 */
.table {
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    background-color: #343a40;
    color: #ffffff;
    position: sticky;
    top: 0;
    z-index: 10;
}

.table td {
    vertical-align: middle;
    border-color: #e9ecef;
    background-color: #ffffff;
    color: #212529;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: #f8f9fa;
}

.table-striped tbody tr:nth-of-type(odd) td {
    background-color: #f8f9fa;
    color: #212529;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.1);
    cursor: pointer;
}

.table-hover tbody tr:hover td {
    background-color: rgba(0, 123, 255, 0.1);
    color: #212529;
}

/* 数据单元格样式 */
.data-cell {
    display: inline-block;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #212529;
    background-color: transparent;
    padding: 2px 4px;
    border-radius: 3px;
}

/* EventString字段特殊样式 */
.event-string-cell {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    max-width: 600px;
    min-height: 2.5em;
}

.event-string-preview {
    flex: 1;
    color: #212529;
    font-size: 0.9rem;
    line-height: 1.5;
    word-break: break-word;
    white-space: normal;
    max-height: 4.5em;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    padding: 4px 0;
}

.event-string-cell .btn {
    flex-shrink: 0;
    padding: 4px 8px;
    font-size: 0.75rem;
    line-height: 1.2;
    white-space: nowrap;
    margin-top: 2px;
}

.event-string-cell .btn i {
    margin-right: 2px;
}

/* EventString表头样式 */
.event-string-header {
    min-width: 300px;
    width: 35%;
}

/* 完整文本模态框样式 */
#fullTextModal .modal-body {
    max-height: 60vh;
    overflow-y: auto;
}

#fullTextContent {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 1rem;
    color: #212529;
    max-height: 50vh;
    overflow-y: auto;
}

/* 响应式表格 */
.table-responsive {
    border-radius: 8px;
    max-height: 600px;
    overflow-y: auto;
    background-color: #ffffff;
    border: 1px solid #dee2e6;
}

/* 确保表格内容可见性 */
.table-responsive .table {
    background-color: #ffffff;
}

.table-responsive .table tbody tr {
    background-color: #ffffff;
}

.table-responsive .table tbody tr td {
    color: #212529 !important;
    background-color: inherit;
}

/* 表格行高亮 */
.table-warning {
    background-color: rgba(255, 193, 7, 0.3) !important;
}

.table-warning td {
    background-color: rgba(255, 193, 7, 0.3) !important;
    color: #212529 !important;
    font-weight: 500;
}

/* 面包屑样式 */
.breadcrumb {
    background-color: transparent;
    padding: 0;
    margin-bottom: 1rem;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: ">";
    color: #6c757d;
}

/* 表单样式 */
.form-control, .form-select {
    border-radius: 6px;
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus, .form-select:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 警告框样式 */
.alert {
    border: none;
    border-radius: 8px;
    font-weight: 500;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

/* 模态框样式 */
.modal-content {
    border: none;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.modal-header {
    border-bottom: 1px solid #e9ecef;
    border-radius: 10px 10px 0 0;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
    border-radius: 0 0 10px 10px;
}

/* 页脚样式 */
footer {
    margin-top: auto;
    border-top: 1px solid #e9ecef;
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 图标样式 */
.fa-3x {
    font-size: 3em;
}

.fa-4x {
    font-size: 4em;
}

/* 代码样式 */
code {
    background-color: #f8f9fa;
    color: #e83e8c;
    padding: 0.2rem 0.4rem;
    border-radius: 4px;
    font-size: 0.875em;
}

/* 自定义滚动条 */
.table-responsive::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 字段控制面板样式 */
#columnControlPanel {
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.column-checkbox {
    cursor: pointer;
}

.form-check-label {
    cursor: pointer;
    font-size: 0.9rem;
    user-select: none;
}

.form-check {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.form-check:hover {
    background-color: rgba(0, 123, 255, 0.1);
}

/* 隐藏的列样式 */
.column-hidden {
    display: none !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .card-body {
        padding: 1rem;
    }

    .table-card .card-body {
        padding: 1.5rem;
    }

    .btn-group {
        display: flex;
        flex-direction: column;
    }

    .btn-group .btn {
        border-radius: 6px !important;
        margin-bottom: 0.25rem;
    }

    .btn-group .btn:last-child {
        margin-bottom: 0;
    }

    /* 移动设备上的字段控制 */
    #columnCheckboxes .col-md-3 {
        flex: 0 0 50%;
        max-width: 50%;
    }
}

@media (max-width: 576px) {
    .container {
        padding-left: 10px;
        padding-right: 10px;
    }
    
    .card {
        margin-bottom: 1rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .data-cell {
        max-width: 150px;
    }
}

/* AGV分析页面样式 */
.agv-analytics-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.agv-analytics-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
}

.statistics-overview .card {
    border: none;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.statistics-overview .card-body {
    padding: 1.5rem;
}

.statistics-overview .fa-2x {
    margin-bottom: 0.5rem;
}

/* 图表容器样式 */
.chart-container {
    position: relative;
    height: 350px;
    margin: 1rem 0;
    transition: all 0.3s ease;
}

.chart-container:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.chart-container canvas {
    max-height: 100%;
    cursor: pointer;
}

/* 图表交互样式 */
.chart-interactive {
    border: 2px solid transparent;
    border-radius: 8px;
    transition: border-color 0.3s ease;
}

.chart-interactive:hover {
    border-color: #007bff;
}

.chart-interactive.active {
    border-color: #28a745;
    background-color: rgba(40, 167, 69, 0.05);
}

/* AGV分析页面样式 */
#statisticsOverview .card,
#chartsAndDataContainer .card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

#statisticsOverview .card-header,
#chartsAndDataContainer .card-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    font-weight: 600;
}

/* 统计概览样式 */
#statisticsOverview .card-body {
    padding: 1.5rem;
}

#statisticsOverview .card-title {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

/* 图表和数据区域样式 */
#chartsAndDataContainer .card-body {
    padding: 1.5rem;
}

#chartsAndDataContainer canvas {
    max-width: 100%;
    height: auto;
}

/* AGV排行榜图表容器 */
#chartsAndDataContainer .chart-container {
    position: relative;
    width: 100%;
    height: 300px;
    margin: 0 auto;
}

#agvRankingChart {
    max-width: 100%;
    max-height: 100%;
}

/* 时间范围选择器样式 */
.time-range-selector {
    padding: 0.5rem;
}

.time-range-selector .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.25rem;
}

.time-range-selector .form-control-sm {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    border: 1px solid #ced4da;
}

.time-range-selector .form-control-sm:focus {
    border-color: #17a2b8;
    box-shadow: 0 0 0 0.1rem rgba(23, 162, 184, 0.25);
}

.time-range-selector .btn-sm {
    font-size: 0.75rem;
    padding: 0.25rem 0.75rem;
    border-radius: 0.25rem;
}

/* AGV排行榜表格样式 */
#agvStatsTable .badge {
    font-size: 0.8rem;
}

#agvStatsTable tr:hover {
    background-color: rgba(0, 123, 255, 0.1);
}

/* 加载动画 */
.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* AGV分析快捷入口样式 */
.agv-analysis-shortcut {
    background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
    border: none;
    color: #212529;
}

.agv-analysis-shortcut .card-body {
    padding: 2rem;
}

.agv-analysis-shortcut .fa-2x {
    margin-bottom: 1rem;
}

/* 统计概览强制同行显示 - 覆盖所有Bootstrap默认行为 */
#statisticsOverview {
    display: flex !important;
    flex-wrap: nowrap !important;
    flex-direction: row !important;
}

#statisticsOverview .col-3 {
    flex: 0 0 25% !important;
    max-width: 25% !important;
    min-width: 25% !important;
    width: 25% !important;
    display: block !important;
}

#statisticsOverview .row {
    --bs-gutter-x: 0.5rem;
    display: flex !important;
    flex-wrap: nowrap !important;
    flex-direction: row !important;
}

/* 强制覆盖Bootstrap的响应式行为 */
#statisticsOverview .col-3,
#statisticsOverview .col-sm-3,
#statisticsOverview .col-md-3,
#statisticsOverview .col-lg-3,
#statisticsOverview .col-xl-3 {
    flex: 0 0 25% !important;
    max-width: 25% !important;
    width: 25% !important;
}

/* 响应式布局 */
@media (max-width: 992px) {
    #statisticsOverview {
        display: flex !important;
        flex-wrap: nowrap !important;
    }

    #statisticsOverview .col-3 {
        flex: 0 0 25% !important;
        max-width: 25% !important;
        min-width: 25% !important;
        width: 25% !important;
    }

    #statisticsOverview .card-body {
        padding: 0.75rem;
    }

    #statisticsOverview .card-title {
        font-size: 1.25rem;
    }

    #statisticsOverview .card-text {
        font-size: 0.8rem;
    }

    #statisticsOverview i {
        font-size: 1.5rem !important;
    }
}

@media (max-width: 768px) {
    .chart-container {
        height: 300px;
    }

    #statisticsOverview {
        display: flex !important;
        flex-wrap: nowrap !important;
    }

    #statisticsOverview .col-3 {
        flex: 0 0 25% !important;
        max-width: 25% !important;
        min-width: 25% !important;
        width: 25% !important;
    }

    .agv-analysis-shortcut .row.text-center .col-md-3 {
        margin-bottom: 1.5rem;
    }

    #statisticsOverview .card-body {
        padding: 0.5rem;
    }

    #chartsAndDataContainer .card-body {
        padding: 1rem;
    }

    #chartsAndDataContainer .chart-container {
        height: 250px;
    }

    .time-range-selector {
        padding: 0.25rem;
    }

    .time-range-selector .form-control-sm {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
    }

    .time-range-selector .btn-sm {
        font-size: 0.7rem;
        padding: 0.2rem 0.5rem;
    }

    #statisticsOverview .card-title {
        font-size: 1rem;
    }

    #statisticsOverview .card-text {
        font-size: 0.7rem;
    }

    #statisticsOverview i {
        font-size: 1.2rem !important;
    }
}

@media (max-width: 576px) {
    #statisticsOverview {
        display: flex !important;
        flex-wrap: nowrap !important;
    }

    #statisticsOverview .col-3 {
        flex: 0 0 25% !important;
        max-width: 25% !important;
        min-width: 25% !important;
        width: 25% !important;
    }

    #statisticsOverview .card-body {
        padding: 0.25rem;
    }

    #chartsAndDataContainer .chart-container {
        height: 200px;
    }

    #chartsAndDataContainer .card-body {
        padding: 0.75rem;
    }

    .time-range-selector {
        padding: 0.1rem;
    }

    .time-range-selector .form-label {
        font-size: 0.65rem;
        margin-bottom: 0.1rem;
    }

    .time-range-selector .form-control-sm {
        font-size: 0.65rem;
        padding: 0.15rem 0.3rem;
    }

    .time-range-selector .btn-sm {
        font-size: 0.65rem;
        padding: 0.15rem 0.4rem;
    }

    #statisticsOverview .card-title {
        font-size: 0.8rem;
        margin-bottom: 0.25rem;
    }

    #statisticsOverview .card-text {
        font-size: 0.6rem;
        margin-bottom: 0;
    }

    #statisticsOverview i {
        font-size: 1rem !important;
        margin-bottom: 0.25rem !important;
    }
}

/* 打印样式 */
@media print {
    .navbar,
    .btn,
    .card-header,
    footer {
        display: none !important;
    }

    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }

    .table {
        font-size: 12px;
    }

    .chart-container {
        page-break-inside: avoid;
    }
}
