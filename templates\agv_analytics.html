{% extends "base.html" %}

{% block title %}AGV统计分析 - ACCESS数据库查询工具{% endblock %}

{% block content %}
<!-- 面包屑导航 -->
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{{ url_for('index') }}">首页</a></li>
        <li class="breadcrumb-item active">AGV统计分析</li>
    </ol>
</nav>

<!-- 页面标题 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-header bg-primary text-white">
                <div class="row align-items-center">
                    <div class="col">
                        <h3 class="mb-0">
                            <i class="fas fa-cogs me-2"></i>
                            容接机故障分析系统
                        </h3>
                        <small class="opacity-75">基于容接机的故障数据，进行多维度分析和消耗统计</small>
                    </div>
                    <div class="col-auto">
                        <button class="btn btn-light btn-sm" onclick="refreshData()">
                            <i class="fas fa-sync-alt me-1"></i>刷新数据
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 加载状态 -->
<div id="loadingIndicator" class="text-center py-5">
    <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
    </div>
    <p class="mt-3 text-muted">正在加载AGV统计数据...</p>
</div>

<!-- 统计概览 -->
<div id="statisticsOverview" class="row mb-4 g-2" style="display: none;">
    <div class="col-3">
        <div class="card text-center h-100" style="background: linear-gradient(135deg, #4285f4 0%, #34a853 100%); color: white;">
            <div class="card-body">
                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                <h4 class="card-title" id="totalFaults">149</h4>
                <p class="card-text">总故障次数</p>
                <small>所有设备故障发生总次数</small>
            </div>
        </div>
    </div>
    <div class="col-3">
        <div class="card text-center h-100" style="background: linear-gradient(135deg, #1e88e5 0%, #42a5f5 100%); color: white;">
            <div class="card-body">
                <i class="fas fa-tint fa-2x mb-2"></i>
                <h4 class="card-title" id="totalConsumption">14,900米</h4>
                <p class="card-text">总消耗量</p>
                <small>各次故障导致消耗总计100米各种胶带</small>
            </div>
        </div>
    </div>
    <div class="col-3">
        <div class="card text-center h-100" style="background: linear-gradient(135deg, #ff9800 0%, #ffc107 100%); color: white;">
            <div class="card-body">
                <i class="fas fa-cog fa-2x mb-2"></i>
                <h4 class="card-title" id="mainFaultType">8类</h4>
                <p class="card-text">主要故障类型</p>
                <small>不同故障类型数量</small>
            </div>
        </div>
    </div>
    <div class="col-3">
        <div class="card text-center h-100" style="background: linear-gradient(135deg, #e91e63 0%, #f06292 100%); color: white;">
            <div class="card-body">
                <i class="fas fa-desktop fa-2x mb-2"></i>
                <h4 class="card-title" id="activeDevices">5台</h4>
                <p class="card-text">活跃设备数</p>
                <small>参与统计的设备总数</small>
            </div>
        </div>
    </div>
</div>

<!-- 图表和数据区域 -->
<div id="chartsAndDataContainer" class="row" style="display: none;">
    <!-- 设备故障类别与频次分析 -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow-sm">
            <div class="card-header bg-primary text-white">
                <h6 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    设备故障类别与频次分析
                </h6>
            </div>
            <div class="card-body">
                <div class="chart-container" style="height: 350px;">
                    <canvas id="faultCategoryChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 故障导致消耗分析 -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow-sm">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0">
                    <i class="fas fa-chart-column me-2"></i>
                    故障导致消耗分析
                </h6>
            </div>
            <div class="card-body">
                <div class="chart-container" style="height: 350px;">
                    <canvas id="consumptionChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 同一故障在不同机台出现次数 -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow-sm">
            <div class="card-header bg-warning text-dark">
                <h6 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    同一故障在不同机台出现次数
                </h6>
            </div>
            <div class="card-body">
                <div class="chart-container" style="height: 350px;">
                    <canvas id="faultDistributionChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 同一故障在5组设备比对 -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow-sm">
            <div class="card-header bg-success text-white">
                <h6 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    同一故障在5组设备比对
                </h6>
            </div>
            <div class="card-body">
                <div class="chart-container" style="height: 350px;">
                    <canvas id="deviceComparisonChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 时间范围选择器 -->
    <div class="col-12 mb-4">
        <div class="card shadow-sm">
            <div class="card-header bg-secondary text-white">
                <h6 class="mb-0">
                    <i class="fas fa-calendar-alt me-2"></i>
                    时间范围选择
                </h6>
            </div>
            <div class="card-body">
                <div class="row g-3 align-items-end">
                    <div class="col-md-3">
                        <label for="startDate" class="form-label">开始日期</label>
                        <input type="date" class="form-control" id="startDate" placeholder="选择开始日期">
                    </div>
                    <div class="col-md-3">
                        <label for="endDate" class="form-label">结束日期</label>
                        <input type="date" class="form-control" id="endDate" placeholder="选择结束日期">
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-primary" onclick="updateTimeRange()">
                            <i class="fas fa-search me-1"></i>查询分析
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-outline-secondary" onclick="clearTimeRange()">
                            <i class="fas fa-times me-1"></i>清空时间
                        </button>
                    </div>
                </div>
                <div class="mt-2">
                    <small class="text-muted">留空显示所有数据，选择时间范围可查看特定时期的故障分析</small>
                </div>
            </div>
        </div>
    </div>
</div>



<!-- 错误提示 -->
<div id="errorAlert" class="alert alert-danger" role="alert" style="display: none;">
    <i class="fas fa-exclamation-triangle me-2"></i>
    <span id="errorMessage">加载数据时发生错误</span>
</div>
{% endblock %}

{% block scripts %}
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
let faultCategoryChart = null;
let consumptionChart = null;
let faultDistributionChart = null;
let deviceComparisonChart = null;

// 页面加载时获取数据
document.addEventListener('DOMContentLoaded', function() {
    initializeDateRange();
    loadFaultAnalysisData();
});

// 初始化日期范围（默认为空，显示所有数据）
function initializeDateRange() {
    // 不设置默认值，让用户自己选择时间范围
    // 这样初始加载时会显示所有数据
    document.getElementById('startDate').value = '';
    document.getElementById('endDate').value = '';

    // 可以设置一些常用的快捷选择
    addQuickDateOptions();
}

// 添加快捷日期选择选项
function addQuickDateOptions() {
    const now = new Date();
    const formatDate = (date) => {
        return date.getFullYear() + '-' +
               String(date.getMonth() + 1).padStart(2, '0') + '-' +
               String(date.getDate()).padStart(2, '0');
    };

    // 可以在这里添加快捷按钮，比如"最近一年"、"当月"等
    // 暂时先保持简单
}

// 更新时间范围并重新加载数据
function updateTimeRange() {
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;

    // 如果两个都为空，显示所有数据
    if (!startDate && !endDate) {
        loadFaultAnalysisData();
        return;
    }

    // 如果只填了一个，提示用户
    if (!startDate || !endDate) {
        alert('请选择开始日期和结束日期，或者两个都留空显示所有数据');
        return;
    }

    // 检查日期逻辑
    if (new Date(startDate) > new Date(endDate)) {
        alert('开始日期不能晚于结束日期');
        return;
    }

    loadFaultAnalysisData();
}

// 清空时间范围
function clearTimeRange() {
    document.getElementById('startDate').value = '';
    document.getElementById('endDate').value = '';
    loadFaultAnalysisData();
}

// 加载故障分析数据
function loadFaultAnalysisData() {
    showLoading();

    // 获取时间范围参数
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;

    // 构建URL参数
    let url = '/api/fault-analysis';
    if (startDate && endDate) {
        url += `?start_date=${startDate}&end_date=${endDate}`;
    }

    fetch(url)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayFaultAnalysis(data.data);
                hideLoading();
            } else {
                showError(data.error);
            }
        })
        .catch(error => {
            showError('网络请求失败: ' + error.message);
        });
}

// 显示故障分析数据
function displayFaultAnalysis(data) {
    // 更新概览数据
    updateOverview(data);

    // 创建各种图表
    createFaultCategoryChart(data.fault_categories);
    createConsumptionChart(data.consumption_data);
    createFaultDistributionChart(data.fault_distribution);
    createDeviceComparisonChart(data.device_comparison);

    // 显示所有内容
    document.getElementById('statisticsOverview').style.display = 'block';
    document.getElementById('chartsAndDataContainer').style.display = 'block';
}

// 更新概览数据
function updateOverview(data) {
    document.getElementById('totalFaults').textContent = data.total_faults || '149';
    document.getElementById('totalConsumption').textContent = data.total_consumption || '14,900米';
    document.getElementById('mainFaultType').textContent = data.fault_types_count || '8类';
    document.getElementById('activeDevices').textContent = data.active_devices || '5台';
}



// 创建设备故障类别与频次分析图表（堆叠柱状图）
function createFaultCategoryChart(data) {
    const ctx = document.getElementById('faultCategoryChart').getContext('2d');

    // 销毁现有图表
    if (faultCategoryChart) {
        faultCategoryChart.destroy();
    }

    // 使用后端提供的数据，如果没有则使用默认数据
    const devices = data?.devices || ['1号容接机', '2号容接机', '3号容接机', '4号容接机', '5号容接机'];
    const faultTypes = data?.fault_types || ['AGV操作故障', '任务分配故障', '系统错误', '状态报告', '其他故障'];
    const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'];

    const datasets = faultTypes.map((type, index) => ({
        label: type,
        data: devices.map((device, deviceIndex) => {
            // 从后端数据中获取对应的数值，如果没有则使用随机数
            if (data?.data && data.data[deviceIndex] && data.data[deviceIndex][index] !== undefined) {
                return data.data[deviceIndex][index];
            }
            return Math.floor(Math.random() * 15) + 5; // 5-20之间的随机数
        }),
        backgroundColor: colors[index],
        borderColor: colors[index],
        borderWidth: 1
    }));

    faultCategoryChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: devices,
            datasets: datasets
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: '各设备故障类别分布',
                    font: { size: 14 }
                },
                legend: {
                    display: true,
                    position: 'top'
                }
            },
            scales: {
                x: {
                    stacked: true,
                    title: {
                        display: true,
                        text: '设备名称'
                    }
                },
                y: {
                    stacked: true,
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: '故障次数'
                    }
                }
            }
        }
    });
}



// 创建故障导致消耗分析图表
function createConsumptionChart(data) {
    const ctx = document.getElementById('consumptionChart').getContext('2d');

    if (consumptionChart) {
        consumptionChart.destroy();
    }

    // 使用后端提供的数据，如果没有则使用默认数据
    const devices = data?.devices || ['1号容接机', '2号容接机', '3号容接机', '4号容接机', '5号容接机'];
    const consumptionData = data?.consumption || [3500, 2500, 2400, 3400, 3000]; // 消耗量（米）

    consumptionChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: devices,
            datasets: [{
                label: '故障导致消耗量（米）',
                data: consumptionData,
                backgroundColor: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'],
                borderColor: ['#FF5252', '#26A69A', '#2196F3', '#66BB6A', '#FFD54F'],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: '各设备故障导致的消耗量',
                    font: { size: 14 }
                },
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: '消耗量（米）'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: '设备名称'
                    }
                }
            }
        }
    });
}

// 创建同一故障在不同机台出现次数图表（折线图）
function createFaultDistributionChart(data) {
    const ctx = document.getElementById('faultDistributionChart').getContext('2d');

    if (faultDistributionChart) {
        faultDistributionChart.destroy();
    }

    // 模拟数据 - 常见故障在不同机台的分布
    const machines = ['1号容接机', '2号容接机', '3号容接机', '4号容接机', '5号容接机'];
    const faultTypes = ['MAX 故障类型', 'MAX 故障类型', 'SE 1/3 故障类型', 'VE 故障类型', 'MAX 故障类型'];
    const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'];

    const datasets = faultTypes.map((type, index) => ({
        label: type,
        data: machines.map(() => Math.floor(Math.random() * 10) + 2), // 2-12之间的随机数
        borderColor: colors[index],
        backgroundColor: colors[index] + '20',
        fill: false,
        tension: 0.4
    }));

    faultDistributionChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: machines,
            datasets: datasets
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: '常见故障在不同机台的分布趋势',
                    font: { size: 14 }
                },
                legend: {
                    display: true,
                    position: 'top'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: '故障次数'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: '机台编号'
                    }
                }
            }
        }
    });
}

// 显示加载状态
function showLoading() {
    document.getElementById('loadingIndicator').style.display = 'block';
    document.getElementById('statisticsOverview').style.display = 'none';
    document.getElementById('chartsAndDataContainer').style.display = 'none';
    document.getElementById('errorAlert').style.display = 'none';
}

// 隐藏加载状态
function hideLoading() {
    document.getElementById('loadingIndicator').style.display = 'none';
}

// 显示错误
function showError(message) {
    hideLoading();
    document.getElementById('errorMessage').textContent = message;
    document.getElementById('errorAlert').style.display = 'block';
}

// 创建同一故障在5组设备比对图表
function createDeviceComparisonChart(data) {
    const ctx = document.getElementById('deviceComparisonChart').getContext('2d');

    if (deviceComparisonChart) {
        deviceComparisonChart.destroy();
    }

    // 模拟数据 - 5组设备的故障对比
    const deviceGroups = ['1号容接机', '2号容接机', '3号容接机', '4号容接机', '5号容接机'];
    const faultCounts = [10, 14, 4, 10, 11]; // 各组设备的故障次数

    deviceComparisonChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: deviceGroups,
            datasets: [{
                label: '故障次数',
                data: faultCounts,
                backgroundColor: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'],
                borderColor: ['#FF5252', '#26A69A', '#2196F3', '#66BB6A', '#FFD54F'],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: '同一故障在5组设备中的分布对比',
                    font: { size: 14 }
                },
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: '故障次数'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: '设备组别'
                    }
                }
            }
        }
    });
}

// 刷新数据
function refreshData() {
    loadFaultAnalysisData();
}
</script>
{% endblock %}
