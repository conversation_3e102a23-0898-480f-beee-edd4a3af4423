{% extends "base.html" %}

{% block title %}AGV统计分析 - ACCESS数据库查询工具{% endblock %}

{% block content %}
<!-- 面包屑导航 -->
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{{ url_for('index') }}">首页</a></li>
        <li class="breadcrumb-item active">AGV统计分析</li>
    </ol>
</nav>

<!-- 页面标题 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-header bg-primary text-white">
                <div class="row align-items-center">
                    <div class="col">
                        <h3 class="mb-0">
                            <i class="fas fa-chart-bar me-2"></i>
                            AGV运行统计分析
                        </h3>
                        <small class="opacity-75">基于EventLog表SourceID字段的数据分析</small>
                    </div>
                    <div class="col-auto">
                        <button class="btn btn-light btn-sm" onclick="refreshData()">
                            <i class="fas fa-sync-alt me-1"></i>刷新数据
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 加载状态 -->
<div id="loadingIndicator" class="text-center py-5">
    <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
    </div>
    <p class="mt-3 text-muted">正在加载AGV统计数据...</p>
</div>

<!-- 统计概览 -->
<div id="statisticsOverview" class="row mb-4 g-2" style="display: none;">
    <div class="col-3">
        <div class="card text-center h-100">
            <div class="card-body">
                <i class="fas fa-robot fa-2x text-primary mb-2"></i>
                <h4 class="card-title" id="totalAgvs">0</h4>
                <p class="card-text text-muted">活跃AGV数量</p>
            </div>
        </div>
    </div>
    <div class="col-3">
        <div class="card text-center h-100">
            <div class="card-body">
                <i class="fas fa-list-ol fa-2x text-success mb-2"></i>
                <h4 class="card-title" id="totalEvents">0</h4>
                <p class="card-text text-muted">总事件数量</p>
            </div>
        </div>
    </div>
    <div class="col-3">
        <div class="card text-center h-100">
            <div class="card-body">
                <i class="fas fa-calendar-alt fa-2x text-info mb-2"></i>
                <div class="time-range-selector">
                    <div class="mb-2">
                        <label for="startDate" class="form-label" style="font-size: 0.8rem;">开始日期</label>
                        <input type="date" class="form-control form-control-sm" id="startDate" placeholder="选择开始日期">
                    </div>
                    <div class="mb-2">
                        <label for="endDate" class="form-label" style="font-size: 0.8rem;">结束日期</label>
                        <input type="date" class="form-control form-control-sm" id="endDate" placeholder="选择结束日期">
                    </div>
                    <div class="mb-2">
                        <small class="text-muted" style="font-size: 0.65rem;">留空显示所有数据</small>
                    </div>
                    <div class="d-flex gap-1">
                        <button class="btn btn-info btn-sm flex-fill" onclick="updateTimeRange()">
                            <i class="fas fa-search me-1"></i>查询
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="clearTimeRange()" title="清空时间范围">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-3">
        <div class="card text-center h-100">
            <div class="card-body">
                <i class="fas fa-crown fa-2x text-warning mb-2"></i>
                <h4 class="card-title" id="topAgv">-</h4>
                <p class="card-text text-muted">最活跃AGV</p>
            </div>
        </div>
    </div>
</div>

<!-- 图表和数据区域 -->
<div id="chartsAndDataContainer" class="row" style="display: none;">
    <!-- AGV运行次数排行榜 -->
    <div class="col-12 mb-4">
        <div class="card shadow-sm">
            <div class="card-header bg-success text-white">
                <h6 class="mb-0">
                    <i class="fas fa-trophy me-2"></i>
                    AGV运行次数排行榜
                </h6>
            </div>
            <div class="card-body">
                <div class="chart-container" style="height: 300px;">
                    <canvas id="agvRankingChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- EventCode统计分析 -->
    <div class="col-12 mb-4">
        <div class="card shadow-sm">
            <div class="card-header bg-warning text-dark">
                <h6 class="mb-0">
                    <i class="fas fa-code me-2"></i>
                    EventCode统计分析
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover table-sm" id="eventCodeStatsTable">
                        <thead class="table-dark">
                            <tr>
                                <th>排名</th>
                                <th>EventCode</th>
                                <th>出现次数</th>
                                <th>占比</th>
                                <th>频率</th>
                            </tr>
                        </thead>
                        <tbody id="eventCodeStatsTableBody">
                            <!-- 数据将通过JavaScript填充 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 详细数据表格 -->
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-header bg-secondary text-white">
                <h6 class="mb-0">
                    <i class="fas fa-table me-2"></i>
                    详细统计数据
                </h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover table-sm" id="agvStatsTable">
                        <thead class="table-dark">
                            <tr>
                                <th>排名</th>
                                <th>AGV编号</th>
                                <th>运行次数</th>
                                <th>占比</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody id="agvStatsTableBody">
                            <!-- 数据将通过JavaScript填充 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>



<!-- 错误提示 -->
<div id="errorAlert" class="alert alert-danger" role="alert" style="display: none;">
    <i class="fas fa-exclamation-triangle me-2"></i>
    <span id="errorMessage">加载数据时发生错误</span>
</div>
{% endblock %}

{% block scripts %}
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
let agvRankingChart = null;

// 页面加载时获取数据
document.addEventListener('DOMContentLoaded', function() {
    initializeDateRange();
    loadAGVStatistics();
});

// 初始化日期范围（默认为空，显示所有数据）
function initializeDateRange() {
    // 不设置默认值，让用户自己选择时间范围
    // 这样初始加载时会显示所有数据
    document.getElementById('startDate').value = '';
    document.getElementById('endDate').value = '';

    // 可以设置一些常用的快捷选择
    addQuickDateOptions();
}

// 添加快捷日期选择选项
function addQuickDateOptions() {
    const now = new Date();
    const formatDate = (date) => {
        return date.getFullYear() + '-' +
               String(date.getMonth() + 1).padStart(2, '0') + '-' +
               String(date.getDate()).padStart(2, '0');
    };

    // 可以在这里添加快捷按钮，比如"最近一年"、"当月"等
    // 暂时先保持简单
}

// 更新时间范围并重新加载数据
function updateTimeRange() {
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;

    // 如果两个都为空，显示所有数据
    if (!startDate && !endDate) {
        loadAGVStatistics();
        return;
    }

    // 如果只填了一个，提示用户
    if (!startDate || !endDate) {
        alert('请选择开始日期和结束日期，或者两个都留空显示所有数据');
        return;
    }

    // 检查日期逻辑
    if (new Date(startDate) > new Date(endDate)) {
        alert('开始日期不能晚于结束日期');
        return;
    }

    loadAGVStatistics();
}

// 清空时间范围
function clearTimeRange() {
    document.getElementById('startDate').value = '';
    document.getElementById('endDate').value = '';
    loadAGVStatistics();
}

// 加载AGV统计数据
function loadAGVStatistics() {
    showLoading();

    // 获取时间范围参数
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;

    // 构建URL参数
    let url = '/api/agv-statistics';
    if (startDate && endDate) {
        url += `?start_date=${startDate}&end_date=${endDate}`;
    }

    fetch(url)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayStatistics(data.data);
                hideLoading();
            } else {
                showError(data.error);
            }
        })
        .catch(error => {
            showError('网络请求失败: ' + error.message);
        });
}

// 显示统计数据
function displayStatistics(data) {
    // 更新概览数据
    updateOverview(data);

    // 创建图表
    createRankingChart(data.agv_ranking);

    // 填充EventCode统计表格
    fillEventCodeTable(data.event_codes);

    // 填充详细表格
    fillDetailTable(data.agv_ranking);

    // 显示所有内容
    document.getElementById('statisticsOverview').style.display = 'block';
    document.getElementById('chartsAndDataContainer').style.display = 'block';
}

// 更新概览数据
function updateOverview(data) {
    document.getElementById('totalAgvs').textContent = data.total_agvs;
    document.getElementById('totalEvents').textContent = data.total_events.toLocaleString();

    if (data.agv_ranking.length > 0) {
        const topAgv = data.agv_ranking[0];
        document.getElementById('topAgv').textContent = `${topAgv.agv_id} (${topAgv.count}次)`;
    }
}



// 创建排行榜图表
function createRankingChart(agvData) {
    const ctx = document.getElementById('agvRankingChart').getContext('2d');
    
    // 销毁现有图表
    if (agvRankingChart) {
        agvRankingChart.destroy();
    }
    
    // 显示所有AGV数据，充分利用图表空间
    const displayData = agvData.slice(0, Math.min(agvData.length, 10));
    
    agvRankingChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: displayData.map(item => item.agv_id),
            datasets: [{
                label: '运行次数',
                data: displayData.map(item => item.count),
                backgroundColor: [
                    '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF',
                    '#FF9F40', '#FF6384', '#C9CBCF', '#4BC0C0', '#FF6384'
                ],
                borderColor: [
                    '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF',
                    '#FF9F40', '#FF6384', '#C9CBCF', '#4BC0C0', '#FF6384'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: 'AGV运行次数排行榜',
                    font: {
                        size: 14
                    }
                },
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: '运行次数',
                        font: {
                            size: 12
                        }
                    },
                    ticks: {
                        font: {
                            size: 11
                        }
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'AGV编号',
                        font: {
                            size: 12
                        }
                    },
                    ticks: {
                        font: {
                            size: 11
                        }
                    }
                }
            }
        }
    });
}



// 填充EventCode统计表格
function fillEventCodeTable(eventCodeData) {
    const tbody = document.getElementById('eventCodeStatsTableBody');
    tbody.innerHTML = '';

    // 计算总数用于百分比计算
    const totalCount = eventCodeData.reduce((sum, item) => sum + item.count, 0);

    eventCodeData.forEach((item, index) => {
        const row = document.createElement('tr');

        // 计算百分比
        const percentage = ((item.count / totalCount) * 100).toFixed(2);

        // 排名徽章
        let rankBadge = '';
        if (index === 0) rankBadge = '<span class="badge bg-warning">🥇</span>';
        else if (index === 1) rankBadge = '<span class="badge bg-secondary">🥈</span>';
        else if (index === 2) rankBadge = '<span class="badge bg-warning">🥉</span>';
        else rankBadge = `<span class="badge bg-light text-dark">${index + 1}</span>`;

        // 频率标识
        let frequencyBadge = '';
        if (percentage > 20) frequencyBadge = '<span class="badge bg-danger">极高频</span>';
        else if (percentage > 10) frequencyBadge = '<span class="badge bg-warning">高频</span>';
        else if (percentage > 5) frequencyBadge = '<span class="badge bg-info">中频</span>';
        else if (percentage > 1) frequencyBadge = '<span class="badge bg-success">低频</span>';
        else frequencyBadge = '<span class="badge bg-secondary">极低频</span>';

        // EventCode显示（如果为null或空，显示为"未知"）
        const eventCodeDisplay = item.code || item.code === 0 ? item.code : '<span class="text-muted">未知</span>';

        row.innerHTML = `
            <td>${rankBadge}</td>
            <td><strong>${eventCodeDisplay}</strong></td>
            <td>${item.count.toLocaleString()}</td>
            <td>${percentage}%</td>
            <td>${frequencyBadge}</td>
        `;

        tbody.appendChild(row);
    });
}

// 填充详细表格
function fillDetailTable(agvData) {
    const tbody = document.getElementById('agvStatsTableBody');
    tbody.innerHTML = '';
    
    agvData.forEach((item, index) => {
        const row = document.createElement('tr');
        
        // 排名徽章
        let rankBadge = '';
        if (index === 0) rankBadge = '<span class="badge bg-warning">🥇</span>';
        else if (index === 1) rankBadge = '<span class="badge bg-secondary">🥈</span>';
        else if (index === 2) rankBadge = '<span class="badge bg-warning">🥉</span>';
        else rankBadge = `<span class="badge bg-light text-dark">${index + 1}</span>`;
        
        // 状态标识
        let statusBadge = '';
        if (item.percentage > 20) statusBadge = '<span class="badge bg-success">高频</span>';
        else if (item.percentage > 10) statusBadge = '<span class="badge bg-warning">中频</span>';
        else statusBadge = '<span class="badge bg-secondary">低频</span>';
        
        row.innerHTML = `
            <td>${rankBadge}</td>
            <td><strong>${item.agv_id}</strong></td>
            <td>${item.count.toLocaleString()}</td>
            <td>${item.percentage}%</td>
            <td>${statusBadge}</td>
        `;
        
        tbody.appendChild(row);
    });
}

// 显示加载状态
function showLoading() {
    document.getElementById('loadingIndicator').style.display = 'block';
    document.getElementById('statisticsOverview').style.display = 'none';
    document.getElementById('chartsAndDataContainer').style.display = 'none';
    document.getElementById('errorAlert').style.display = 'none';
}

// 隐藏加载状态
function hideLoading() {
    document.getElementById('loadingIndicator').style.display = 'none';
}

// 显示错误
function showError(message) {
    hideLoading();
    document.getElementById('errorMessage').textContent = message;
    document.getElementById('errorAlert').style.display = 'block';
}

// 刷新数据
function refreshData() {
    loadAGVStatistics();
}
</script>
{% endblock %}
