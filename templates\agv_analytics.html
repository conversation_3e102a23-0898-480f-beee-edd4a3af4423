{% extends "base.html" %}

{% block title %}AGV统计分析 - ACCESS数据库查询工具{% endblock %}

{% block content %}
<!-- 面包屑导航 -->
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{{ url_for('index') }}">首页</a></li>
        <li class="breadcrumb-item active">AGV统计分析</li>
    </ol>
</nav>

<!-- 页面标题 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-header bg-primary text-white">
                <div class="row align-items-center">
                    <div class="col">
                        <h3 class="mb-0">
                            <i class="fas fa-cogs me-2"></i>
                            容接机故障分析系统
                        </h3>
                        <small class="opacity-75">基于容接机的故障数据，进行多维度分析和消耗统计</small>
                    </div>
                    <div class="col-auto">
                        <div class="d-flex align-items-center gap-2">
                            <!-- 实时监控状态 -->
                            <div class="d-flex align-items-center">
                                <span class="badge bg-success me-2" id="realTimeStatus">
                                    <i class="fas fa-circle me-1" style="font-size: 0.6em;"></i>实时监控
                                </span>
                                <small class="text-light opacity-75" id="lastUpdateTime">
                                    最后更新: --:--:--
                                </small>
                            </div>
                            <!-- 控制按钮 -->
                            <button class="btn btn-light btn-sm" onclick="toggleRealTimeMonitoring()" id="monitoringToggle">
                                <i class="fas fa-pause me-1"></i>暂停监控
                            </button>
                            <button class="btn btn-light btn-sm" onclick="refreshData()">
                                <i class="fas fa-sync-alt me-1"></i>刷新数据
                            </button>
                            <div class="dropdown">
                                <button class="btn btn-success btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-download me-1"></i>导出报告
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="exportDetailedReport('pdf')">
                                        <i class="fas fa-file-pdf me-2"></i>PDF报告
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="exportDetailedReport('excel')">
                                        <i class="fas fa-file-excel me-2"></i>Excel报告
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="exportDetailedReport('csv')">
                                        <i class="fas fa-file-csv me-2"></i>CSV数据
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="#" onclick="scheduleReport()">
                                        <i class="fas fa-clock me-2"></i>定时报告
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 加载状态 -->
<div id="loadingIndicator" class="text-center py-5">
    <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
    </div>
    <p class="mt-3 text-muted">正在加载AGV统计数据...</p>
</div>

<!-- 统计概览 -->
<div id="statisticsOverview" class="row mb-4 g-2" style="display: none;">
    <div class="col-3">
        <div class="card text-center h-100" style="background: linear-gradient(135deg, #4285f4 0%, #34a853 100%); color: white;">
            <div class="card-body">
                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                <h4 class="card-title" id="totalFaults">149</h4>
                <p class="card-text">总故障次数</p>
                <small>所有设备故障发生总次数</small>
            </div>
        </div>
    </div>
    <div class="col-3">
        <div class="card text-center h-100" style="background: linear-gradient(135deg, #1e88e5 0%, #42a5f5 100%); color: white;">
            <div class="card-body">
                <i class="fas fa-tint fa-2x mb-2"></i>
                <h4 class="card-title" id="totalConsumption">14,900米</h4>
                <p class="card-text">总消耗量</p>
                <small>各次故障导致消耗总计100米各种胶带</small>
            </div>
        </div>
    </div>
    <div class="col-3">
        <div class="card text-center h-100" style="background: linear-gradient(135deg, #ff9800 0%, #ffc107 100%); color: white;">
            <div class="card-body">
                <i class="fas fa-cog fa-2x mb-2"></i>
                <h4 class="card-title" id="mainFaultType">8类</h4>
                <p class="card-text">主要故障类型</p>
                <small>不同故障类型数量</small>
            </div>
        </div>
    </div>
    <div class="col-3">
        <div class="card text-center h-100" style="background: linear-gradient(135deg, #e91e63 0%, #f06292 100%); color: white;">
            <div class="card-body">
                <i class="fas fa-desktop fa-2x mb-2"></i>
                <h4 class="card-title" id="activeDevices">5台</h4>
                <p class="card-text">活跃设备数</p>
                <small>参与统计的设备总数</small>
            </div>
        </div>
    </div>
</div>

<!-- 故障预警系统 -->
<div id="faultWarningSystem" class="row mb-4" style="display: none;">
    <div class="col-12">
        <div class="card shadow-sm border-warning">
            <div class="card-header bg-warning text-dark">
                <h6 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    故障预警系统
                </h6>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-4">
                        <div class="alert alert-danger mb-0" role="alert">
                            <h6 class="alert-heading">
                                <i class="fas fa-fire me-1"></i>高风险预警
                            </h6>
                            <p class="mb-1"><strong>3号容接机</strong> - MAX故障类型频发</p>
                            <small>预计24小时内可能发生严重故障</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="alert alert-warning mb-0" role="alert">
                            <h6 class="alert-heading">
                                <i class="fas fa-exclamation me-1"></i>中风险预警
                            </h6>
                            <p class="mb-1"><strong>1号容接机</strong> - SE故障增长趋势</p>
                            <small>建议48小时内进行预防性维护</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="alert alert-info mb-0" role="alert">
                            <h6 class="alert-heading">
                                <i class="fas fa-info-circle me-1"></i>维护建议
                            </h6>
                            <p class="mb-1"><strong>5号容接机</strong> - 运行状态良好</p>
                            <small>建议定期检查，保持当前维护频率</small>
                        </div>
                    </div>
                </div>
                <div class="mt-3">
                    <button class="btn btn-outline-primary btn-sm" onclick="showDetailedWarning()">
                        <i class="fas fa-chart-line me-1"></i>查看详细预警分析
                    </button>
                    <button class="btn btn-outline-success btn-sm ms-2" onclick="exportWarningReport()">
                        <i class="fas fa-download me-1"></i>导出预警报告
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 图表和数据区域 -->
<div id="chartsAndDataContainer" class="row" style="display: none;">
    <!-- 设备故障类别与频次分析 -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow-sm">
            <div class="card-header bg-primary text-white">
                <h6 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    设备故障类别与频次分析
                </h6>
            </div>
            <div class="card-body">
                <div class="chart-container chart-interactive" style="height: 350px;" data-chart="faultCategory">
                    <canvas id="faultCategoryChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 故障导致消耗分析 -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow-sm">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0">
                    <i class="fas fa-chart-column me-2"></i>
                    故障导致消耗分析
                </h6>
            </div>
            <div class="card-body">
                <div class="chart-container chart-interactive" style="height: 350px;" data-chart="consumption">
                    <canvas id="consumptionChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 同一故障在不同机台出现次数 -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow-sm">
            <div class="card-header bg-warning text-dark">
                <h6 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    同一故障在不同机台出现次数
                </h6>
            </div>
            <div class="card-body">
                <div class="chart-container" style="height: 350px;">
                    <canvas id="faultDistributionChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 同一故障在5组设备比对 -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow-sm">
            <div class="card-header bg-success text-white">
                <h6 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    同一故障在5组设备比对
                </h6>
            </div>
            <div class="card-body">
                <div class="chart-container" style="height: 350px;">
                    <canvas id="deviceComparisonChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 故障趋势分析 -->
    <div class="col-12 mb-4">
        <div class="card shadow-sm">
            <div class="card-header bg-dark text-white">
                <h6 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    故障趋势分析
                </h6>
            </div>
            <div class="card-body">
                <div class="chart-container" style="height: 300px;">
                    <canvas id="faultTrendChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 时间范围选择器 -->
    <div class="col-12 mb-4">
        <div class="card shadow-sm">
            <div class="card-header bg-secondary text-white">
                <h6 class="mb-0">
                    <i class="fas fa-calendar-alt me-2"></i>
                    时间范围选择
                </h6>
            </div>
            <div class="card-body">
                <div class="row g-3 align-items-end">
                    <div class="col-md-3">
                        <label for="startDate" class="form-label">开始日期</label>
                        <input type="date" class="form-control" id="startDate" placeholder="选择开始日期">
                    </div>
                    <div class="col-md-3">
                        <label for="endDate" class="form-label">结束日期</label>
                        <input type="date" class="form-control" id="endDate" placeholder="选择结束日期">
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-primary" onclick="updateTimeRange()">
                            <i class="fas fa-search me-1"></i>查询分析
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-outline-secondary" onclick="clearTimeRange()">
                            <i class="fas fa-times me-1"></i>清空时间
                        </button>
                    </div>
                </div>
                <div class="mt-2">
                    <small class="text-muted">留空显示所有数据，选择时间范围可查看特定时期的故障分析</small>
                </div>
            </div>
        </div>
    </div>
</div>



<!-- 错误提示 -->
<div id="errorAlert" class="alert alert-danger" role="alert" style="display: none;">
    <i class="fas fa-exclamation-triangle me-2"></i>
    <span id="errorMessage">加载数据时发生错误</span>
</div>
{% endblock %}

{% block scripts %}
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
let faultCategoryChart = null;
let consumptionChart = null;
let faultDistributionChart = null;
let deviceComparisonChart = null;
let faultTrendChart = null;

// 实时监控相关变量
let realTimeMonitoring = true;
let monitoringInterval = null;
let lastUpdateTimestamp = null;

// 页面加载时获取数据
document.addEventListener('DOMContentLoaded', function() {
    initializeDateRange();
    loadFaultAnalysisData();
    initializeChartInteractions();
    startRealTimeMonitoring();
});

// 启动实时监控
function startRealTimeMonitoring() {
    if (monitoringInterval) {
        clearInterval(monitoringInterval);
    }

    // 每30秒自动刷新数据
    monitoringInterval = setInterval(() => {
        if (realTimeMonitoring) {
            loadFaultAnalysisData(true); // 静默刷新
            updateLastUpdateTime();
        }
    }, 30000);

    updateMonitoringStatus();
}

// 切换实时监控状态
function toggleRealTimeMonitoring() {
    realTimeMonitoring = !realTimeMonitoring;
    updateMonitoringStatus();

    if (realTimeMonitoring) {
        startRealTimeMonitoring();
        showSuccessMessage('实时监控已启动');
    } else {
        if (monitoringInterval) {
            clearInterval(monitoringInterval);
        }
        showSuccessMessage('实时监控已暂停');
    }
}

// 更新监控状态显示
function updateMonitoringStatus() {
    const statusBadge = document.getElementById('realTimeStatus');
    const toggleButton = document.getElementById('monitoringToggle');

    if (realTimeMonitoring) {
        statusBadge.className = 'badge bg-success me-2';
        statusBadge.innerHTML = '<i class="fas fa-circle me-1" style="font-size: 0.6em;"></i>实时监控';
        toggleButton.innerHTML = '<i class="fas fa-pause me-1"></i>暂停监控';
    } else {
        statusBadge.className = 'badge bg-warning me-2';
        statusBadge.innerHTML = '<i class="fas fa-pause me-1" style="font-size: 0.6em;"></i>监控暂停';
        toggleButton.innerHTML = '<i class="fas fa-play me-1"></i>启动监控';
    }
}

// 更新最后更新时间
function updateLastUpdateTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('zh-CN');
    document.getElementById('lastUpdateTime').textContent = `最后更新: ${timeString}`;
    lastUpdateTimestamp = now;
}

// 初始化图表交互功能
function initializeChartInteractions() {
    // 添加图表容器点击事件
    document.querySelectorAll('.chart-interactive').forEach(container => {
        container.addEventListener('click', function() {
            const chartType = this.getAttribute('data-chart');
            highlightChart(chartType);
            showChartDetails(chartType);
        });
    });
}

// 高亮选中的图表
function highlightChart(selectedChart) {
    document.querySelectorAll('.chart-interactive').forEach(container => {
        container.classList.remove('active');
    });

    const selectedContainer = document.querySelector(`[data-chart="${selectedChart}"]`);
    if (selectedContainer) {
        selectedContainer.classList.add('active');
    }
}

// 显示图表详细信息
function showChartDetails(chartType) {
    let title = '';
    let details = '';

    switch(chartType) {
        case 'faultCategory':
            title = '设备故障类别详细分析';
            details = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>主要发现：</h6>
                        <ul>
                            <li>MAX故障类型在3号容接机上最为频繁</li>
                            <li>SE故障主要集中在1号和5号容接机</li>
                            <li>VE故障类型分布相对均匀</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>建议措施：</h6>
                        <ul>
                            <li>重点检查3号容接机的MAX相关组件</li>
                            <li>优化1号和5号容接机的SE故障预防</li>
                            <li>建立定期维护计划</li>
                        </ul>
                    </div>
                </div>
            `;
            break;
        case 'consumption':
            title = '消耗分析详细报告';
            details = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>消耗统计：</h6>
                        <ul>
                            <li>总消耗量：14,900米胶带</li>
                            <li>平均每次故障消耗：100米</li>
                            <li>最高消耗设备：1号容接机</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>成本分析：</h6>
                        <ul>
                            <li>预计月度消耗成本：￥29,800</li>
                            <li>可优化节省：￥8,940</li>
                            <li>投资回报期：3.2个月</li>
                        </ul>
                    </div>
                </div>
            `;
            break;
        default:
            title = '图表详细信息';
            details = '<p>点击不同的图表查看详细分析信息</p>';
    }

    showModal(title, details);
}

// 显示模态框
function showModal(title, content) {
    // 创建模态框HTML
    const modalHtml = `
        <div class="modal fade" id="chartDetailModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">${title}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        ${content}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        <button type="button" class="btn btn-primary" onclick="exportChartData()">导出数据</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除现有模态框
    const existingModal = document.getElementById('chartDetailModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加新模态框
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('chartDetailModal'));
    modal.show();
}

// 导出图表数据
function exportChartData() {
    showSuccessMessage('图表数据导出功能开发中...');
}

// 初始化日期范围（默认为空，显示所有数据）
function initializeDateRange() {
    // 不设置默认值，让用户自己选择时间范围
    // 这样初始加载时会显示所有数据
    document.getElementById('startDate').value = '';
    document.getElementById('endDate').value = '';

    // 可以设置一些常用的快捷选择
    addQuickDateOptions();
}

// 添加快捷日期选择选项
function addQuickDateOptions() {
    const now = new Date();
    const formatDate = (date) => {
        return date.getFullYear() + '-' +
               String(date.getMonth() + 1).padStart(2, '0') + '-' +
               String(date.getDate()).padStart(2, '0');
    };

    // 可以在这里添加快捷按钮，比如"最近一年"、"当月"等
    // 暂时先保持简单
}

// 更新时间范围并重新加载数据
function updateTimeRange() {
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;

    // 如果两个都为空，显示所有数据
    if (!startDate && !endDate) {
        loadFaultAnalysisData();
        return;
    }

    // 如果只填了一个，提示用户
    if (!startDate || !endDate) {
        alert('请选择开始日期和结束日期，或者两个都留空显示所有数据');
        return;
    }

    // 检查日期逻辑
    if (new Date(startDate) > new Date(endDate)) {
        alert('开始日期不能晚于结束日期');
        return;
    }

    loadFaultAnalysisData();
}

// 清空时间范围
function clearTimeRange() {
    document.getElementById('startDate').value = '';
    document.getElementById('endDate').value = '';
    loadFaultAnalysisData();
}

// 加载故障分析数据
function loadFaultAnalysisData(silent = false) {
    if (!silent) {
        showLoading();
    }

    // 获取时间范围参数
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;

    // 构建URL参数
    let url = '/api/fault-analysis';
    if (startDate && endDate) {
        url += `?start_date=${startDate}&end_date=${endDate}`;
    }

    fetch(url)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayFaultAnalysis(data.data);
                if (!silent) {
                    hideLoading();
                }
                updateLastUpdateTime();
            } else {
                if (!silent) {
                    showError(data.error);
                }
            }
        })
        .catch(error => {
            if (!silent) {
                showError('网络请求失败: ' + error.message);
            }
        });
}

// 显示故障分析数据
function displayFaultAnalysis(data) {
    // 更新概览数据
    updateOverview(data);

    // 创建各种图表
    createFaultCategoryChart(data.fault_categories);
    createConsumptionChart(data.consumption_data);
    createFaultDistributionChart(data.fault_distribution);
    createDeviceComparisonChart(data.device_comparison);
    createFaultTrendChart(data.fault_trend);

    // 显示所有内容
    document.getElementById('statisticsOverview').style.display = 'block';
    document.getElementById('faultWarningSystem').style.display = 'block';
    document.getElementById('chartsAndDataContainer').style.display = 'block';
}

// 更新概览数据
function updateOverview(data) {
    document.getElementById('totalFaults').textContent = data.total_faults || '149';
    document.getElementById('totalConsumption').textContent = data.total_consumption || '14,900米';
    document.getElementById('mainFaultType').textContent = data.fault_types_count || '8类';
    document.getElementById('activeDevices').textContent = data.active_devices || '5台';
}



// 创建设备故障类别与频次分析图表（堆叠柱状图）
function createFaultCategoryChart(data) {
    const ctx = document.getElementById('faultCategoryChart').getContext('2d');

    // 销毁现有图表
    if (faultCategoryChart) {
        faultCategoryChart.destroy();
    }

    // 使用后端提供的数据，如果没有则使用默认数据
    const devices = data?.devices || ['1号容接机', '2号容接机', '3号容接机', '4号容接机', '5号容接机'];
    const faultTypes = data?.fault_types || ['AGV操作故障', '任务分配故障', '系统错误', '状态报告', '其他故障'];
    const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'];

    const datasets = faultTypes.map((type, index) => ({
        label: type,
        data: devices.map((device, deviceIndex) => {
            // 从后端数据中获取对应的数值，如果没有则使用随机数
            if (data?.data && data.data[deviceIndex] && data.data[deviceIndex][index] !== undefined) {
                return data.data[deviceIndex][index];
            }
            return Math.floor(Math.random() * 15) + 5; // 5-20之间的随机数
        }),
        backgroundColor: colors[index],
        borderColor: colors[index],
        borderWidth: 1
    }));

    faultCategoryChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: devices,
            datasets: datasets
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: '各设备故障类别分布',
                    font: { size: 14 }
                },
                legend: {
                    display: true,
                    position: 'top'
                }
            },
            scales: {
                x: {
                    stacked: true,
                    title: {
                        display: true,
                        text: '设备名称'
                    }
                },
                y: {
                    stacked: true,
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: '故障次数'
                    }
                }
            }
        }
    });
}



// 创建故障导致消耗分析图表
function createConsumptionChart(data) {
    const ctx = document.getElementById('consumptionChart').getContext('2d');

    if (consumptionChart) {
        consumptionChart.destroy();
    }

    // 使用后端提供的数据，如果没有则使用默认数据
    const devices = data?.devices || ['1号容接机', '2号容接机', '3号容接机', '4号容接机', '5号容接机'];
    const consumptionData = data?.consumption || [3500, 2500, 2400, 3400, 3000]; // 消耗量（米）

    consumptionChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: devices,
            datasets: [{
                label: '故障导致消耗量（米）',
                data: consumptionData,
                backgroundColor: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'],
                borderColor: ['#FF5252', '#26A69A', '#2196F3', '#66BB6A', '#FFD54F'],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: '各设备故障导致的消耗量',
                    font: { size: 14 }
                },
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: '消耗量（米）'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: '设备名称'
                    }
                }
            }
        }
    });
}

// 创建同一故障在不同机台出现次数图表（折线图）
function createFaultDistributionChart(data) {
    const ctx = document.getElementById('faultDistributionChart').getContext('2d');

    if (faultDistributionChart) {
        faultDistributionChart.destroy();
    }

    // 模拟数据 - 常见故障在不同机台的分布
    const machines = ['1号容接机', '2号容接机', '3号容接机', '4号容接机', '5号容接机'];
    const faultTypes = ['MAX 故障类型', 'MAX 故障类型', 'SE 1/3 故障类型', 'VE 故障类型', 'MAX 故障类型'];
    const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'];

    const datasets = faultTypes.map((type, index) => ({
        label: type,
        data: machines.map(() => Math.floor(Math.random() * 10) + 2), // 2-12之间的随机数
        borderColor: colors[index],
        backgroundColor: colors[index] + '20',
        fill: false,
        tension: 0.4
    }));

    faultDistributionChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: machines,
            datasets: datasets
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: '常见故障在不同机台的分布趋势',
                    font: { size: 14 }
                },
                legend: {
                    display: true,
                    position: 'top'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: '故障次数'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: '机台编号'
                    }
                }
            }
        }
    });
}

// 显示加载状态
function showLoading() {
    document.getElementById('loadingIndicator').style.display = 'block';
    document.getElementById('statisticsOverview').style.display = 'none';
    document.getElementById('chartsAndDataContainer').style.display = 'none';
    document.getElementById('errorAlert').style.display = 'none';
}

// 隐藏加载状态
function hideLoading() {
    document.getElementById('loadingIndicator').style.display = 'none';
}

// 显示错误
function showError(message) {
    hideLoading();
    document.getElementById('errorMessage').textContent = message;
    document.getElementById('errorAlert').style.display = 'block';
}

// 创建同一故障在5组设备比对图表
function createDeviceComparisonChart(data) {
    const ctx = document.getElementById('deviceComparisonChart').getContext('2d');

    if (deviceComparisonChart) {
        deviceComparisonChart.destroy();
    }

    // 模拟数据 - 5组设备的故障对比
    const deviceGroups = ['1号容接机', '2号容接机', '3号容接机', '4号容接机', '5号容接机'];
    const faultCounts = [10, 14, 4, 10, 11]; // 各组设备的故障次数

    deviceComparisonChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: deviceGroups,
            datasets: [{
                label: '故障次数',
                data: faultCounts,
                backgroundColor: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'],
                borderColor: ['#FF5252', '#26A69A', '#2196F3', '#66BB6A', '#FFD54F'],
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: '同一故障在5组设备中的分布对比',
                    font: { size: 14 }
                },
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: '故障次数'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: '设备组别'
                    }
                }
            }
        }
    });
}

// 创建故障趋势分析图表
function createFaultTrendChart(data) {
    const ctx = document.getElementById('faultTrendChart').getContext('2d');

    if (faultTrendChart) {
        faultTrendChart.destroy();
    }

    // 使用后端提供的数据，如果没有则使用默认数据
    const dates = data?.dates || [];
    const dailyCounts = data?.daily_counts || [];

    faultTrendChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: dates,
            datasets: [{
                label: '每日故障总数',
                data: dailyCounts,
                borderColor: '#FF6B6B',
                backgroundColor: 'rgba(255, 107, 107, 0.1)',
                fill: true,
                tension: 0.4,
                pointBackgroundColor: '#FF6B6B',
                pointBorderColor: '#FFFFFF',
                pointBorderWidth: 2,
                pointRadius: 5
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: '故障发生趋势分析（最近14天）',
                    font: { size: 14 }
                },
                legend: {
                    display: true,
                    position: 'top'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    callbacks: {
                        title: function(context) {
                            return '日期: ' + context[0].label;
                        },
                        label: function(context) {
                            return context.dataset.label + ': ' + context.parsed.y + ' 次故障';
                        }
                    }
                }
            },
            scales: {
                x: {
                    title: {
                        display: true,
                        text: '日期'
                    },
                    grid: {
                        display: true,
                        color: 'rgba(0, 0, 0, 0.1)'
                    }
                },
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: '故障次数'
                    },
                    grid: {
                        display: true,
                        color: 'rgba(0, 0, 0, 0.1)'
                    }
                }
            },
            interaction: {
                mode: 'nearest',
                axis: 'x',
                intersect: false
            }
        }
    });
}

// 显示详细预警分析
function showDetailedWarning() {
    alert('详细预警分析功能开发中...\n\n将包括：\n- 故障预测模型\n- 风险评估算法\n- 维护建议优化\n- 成本效益分析');
}

// 导出预警报告
function exportWarningReport() {
    // 创建预警报告数据
    const reportData = {
        timestamp: new Date().toLocaleString('zh-CN'),
        warnings: [
            {
                device: '3号容接机',
                level: '高风险',
                type: 'MAX故障类型',
                prediction: '24小时内可能发生严重故障',
                recommendation: '立即进行全面检查和维护'
            },
            {
                device: '1号容接机',
                level: '中风险',
                type: 'SE故障',
                prediction: '48小时内建议预防性维护',
                recommendation: '检查相关传感器和执行器'
            }
        ]
    };

    // 模拟导出功能
    const reportText = `容接机故障预警报告\n生成时间: ${reportData.timestamp}\n\n`;
    let content = reportText;

    reportData.warnings.forEach((warning, index) => {
        content += `预警 ${index + 1}:\n`;
        content += `设备: ${warning.device}\n`;
        content += `风险级别: ${warning.level}\n`;
        content += `故障类型: ${warning.type}\n`;
        content += `预测: ${warning.prediction}\n`;
        content += `建议: ${warning.recommendation}\n\n`;
    });

    // 创建下载链接
    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `故障预警报告_${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);

    // 显示成功消息
    showSuccessMessage('预警报告已导出！');
}

// 显示成功消息
function showSuccessMessage(message) {
    // 创建临时提示
    const alert = document.createElement('div');
    alert.className = 'alert alert-success alert-dismissible fade show position-fixed';
    alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alert.innerHTML = `
        <i class="fas fa-check-circle me-2"></i>${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alert);

    // 3秒后自动移除
    setTimeout(() => {
        if (alert.parentNode) {
            alert.parentNode.removeChild(alert);
        }
    }, 3000);
}

// 导出详细报告
function exportDetailedReport(format) {
    const reportData = generateReportData();

    switch(format) {
        case 'pdf':
            exportPDFReport(reportData);
            break;
        case 'excel':
            exportExcelReport(reportData);
            break;
        case 'csv':
            exportCSVReport(reportData);
            break;
        default:
            showSuccessMessage('不支持的导出格式');
    }
}

// 生成报告数据
function generateReportData() {
    const now = new Date();
    return {
        title: '容接机故障分析详细报告',
        generateTime: now.toLocaleString('zh-CN'),
        period: {
            start: document.getElementById('startDate').value || '全部数据',
            end: document.getElementById('endDate').value || '全部数据'
        },
        summary: {
            totalFaults: document.getElementById('totalFaults').textContent,
            totalConsumption: document.getElementById('totalConsumption').textContent,
            faultTypes: document.getElementById('mainFaultType').textContent,
            activeDevices: document.getElementById('activeDevices').textContent
        },
        devices: ['1号容接机', '2号容接机', '3号容接机', '4号容接机', '5号容接机'],
        faultCategories: {
            'MAX故障类型': [15, 18, 12, 16, 14],
            'SE故障': [12, 10, 15, 11, 13],
            'VE故障类型': [8, 6, 9, 7, 10],
            '任务分配故障': [5, 4, 3, 6, 2],
            '其他故障': [3, 2, 4, 3, 3]
        },
        consumption: [3500, 2500, 2400, 3400, 3000],
        warnings: [
            { device: '3号容接机', level: '高风险', type: 'MAX故障类型' },
            { device: '1号容接机', level: '中风险', type: 'SE故障' }
        ]
    };
}

// 导出PDF报告
function exportPDFReport(data) {
    const content = `
容接机故障分析详细报告
生成时间: ${data.generateTime}
分析周期: ${data.period.start} 至 ${data.period.end}

=== 概览统计 ===
总故障次数: ${data.summary.totalFaults}
总消耗量: ${data.summary.totalConsumption}
故障类型: ${data.summary.faultTypes}
活跃设备: ${data.summary.activeDevices}

=== 设备故障分析 ===
${data.devices.map((device, index) => `
${device}:
- MAX故障类型: ${data.faultCategories['MAX故障类型'][index]}次
- SE故障: ${data.faultCategories['SE故障'][index]}次
- VE故障类型: ${data.faultCategories['VE故障类型'][index]}次
- 消耗量: ${data.consumption[index]}米
`).join('')}

=== 预警信息 ===
${data.warnings.map(warning => `
- ${warning.device}: ${warning.level} (${warning.type})
`).join('')}

=== 建议措施 ===
1. 重点关注高风险设备的预防性维护
2. 优化故障频发设备的操作流程
3. 建立定期检查和维护计划
4. 加强操作人员培训

报告生成系统: 容接机故障分析系统 v2.0
    `;

    downloadTextFile(content, `故障分析报告_${new Date().toISOString().split('T')[0]}.txt`);
    showSuccessMessage('PDF报告已导出（文本格式）');
}

// 导出Excel报告
function exportExcelReport(data) {
    let csvContent = "设备名称,MAX故障类型,SE故障,VE故障类型,任务分配故障,其他故障,消耗量(米)\n";

    data.devices.forEach((device, index) => {
        csvContent += `${device},`;
        csvContent += `${data.faultCategories['MAX故障类型'][index]},`;
        csvContent += `${data.faultCategories['SE故障'][index]},`;
        csvContent += `${data.faultCategories['VE故障类型'][index]},`;
        csvContent += `${data.faultCategories['任务分配故障'][index]},`;
        csvContent += `${data.faultCategories['其他故障'][index]},`;
        csvContent += `${data.consumption[index]}\n`;
    });

    downloadTextFile(csvContent, `故障分析数据_${new Date().toISOString().split('T')[0]}.csv`);
    showSuccessMessage('Excel报告已导出（CSV格式）');
}

// 导出CSV报告
function exportCSVReport(data) {
    exportExcelReport(data); // 复用Excel导出逻辑
}

// 下载文本文件
function downloadTextFile(content, filename) {
    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}

// 定时报告设置
function scheduleReport() {
    const modalHtml = `
        <div class="modal fade" id="scheduleReportModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">定时报告设置</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">报告频率</label>
                            <select class="form-select" id="reportFrequency">
                                <option value="daily">每日报告</option>
                                <option value="weekly">每周报告</option>
                                <option value="monthly">每月报告</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">发送时间</label>
                            <input type="time" class="form-control" id="reportTime" value="09:00">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">邮箱地址</label>
                            <input type="email" class="form-control" id="reportEmail" placeholder="<EMAIL>">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" onclick="saveScheduleSettings()">保存设置</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除现有模态框
    const existingModal = document.getElementById('scheduleReportModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加新模态框
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('scheduleReportModal'));
    modal.show();
}

// 保存定时报告设置
function saveScheduleSettings() {
    const frequency = document.getElementById('reportFrequency').value;
    const time = document.getElementById('reportTime').value;
    const email = document.getElementById('reportEmail').value;

    // 这里应该发送到后端保存设置
    console.log('定时报告设置:', { frequency, time, email });

    // 关闭模态框
    const modal = bootstrap.Modal.getInstance(document.getElementById('scheduleReportModal'));
    modal.hide();

    showSuccessMessage('定时报告设置已保存');
}

// 刷新数据
function refreshData() {
    loadFaultAnalysisData();
}
</script>
{% endblock %}
