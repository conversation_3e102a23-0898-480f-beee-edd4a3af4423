{% extends "base.html" %}

{% block title %}错误 - ACCESS数据库查询工具{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card shadow-sm">
            <div class="card-header bg-danger text-white">
                <h4 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    发生错误
                </h4>
            </div>
            <div class="card-body text-center">
                <i class="fas fa-bug fa-4x text-danger mb-4"></i>
                <h5 class="text-danger mb-3">操作失败</h5>
                <div class="alert alert-danger" role="alert">
                    <strong>错误信息:</strong><br>
                    {{ error }}
                </div>
                
                <div class="mt-4">
                    <a href="{{ url_for('index') }}" class="btn btn-primary me-2">
                        <i class="fas fa-home me-1"></i>返回首页
                    </a>
                    <button onclick="history.back()" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>返回上页
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 常见问题解决方案 -->
        <div class="card shadow-sm mt-4">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-question-circle me-2"></i>
                    常见问题解决方案
                </h5>
            </div>
            <div class="card-body">
                <div class="accordion" id="troubleshootAccordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="headingOne">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne">
                                数据库连接失败
                            </button>
                        </h2>
                        <div id="collapseOne" class="accordion-collapse collapse" data-bs-parent="#troubleshootAccordion">
                            <div class="accordion-body">
                                <ul>
                                    <li>检查数据库文件是否存在</li>
                                    <li>确认已安装Microsoft Access Driver</li>
                                    <li>检查文件权限是否正确</li>
                                    <li>确认数据库文件没有被其他程序占用</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="headingTwo">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo">
                                查询失败
                            </button>
                        </h2>
                        <div id="collapseTwo" class="accordion-collapse collapse" data-bs-parent="#troubleshootAccordion">
                            <div class="accordion-body">
                                <ul>
                                    <li>检查表名是否正确</li>
                                    <li>确认表中有数据</li>
                                    <li>检查查询条数限制</li>
                                    <li>确认数据库结构完整</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="headingThree">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree">
                                其他问题
                            </button>
                        </h2>
                        <div id="collapseThree" class="accordion-collapse collapse" data-bs-parent="#troubleshootAccordion">
                            <div class="accordion-body">
                                <ul>
                                    <li>重启应用程序</li>
                                    <li>检查Python环境和依赖包</li>
                                    <li>查看控制台错误信息</li>
                                    <li>联系系统管理员</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
