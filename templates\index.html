{% extends "base.html" %}

{% block title %}首页 - ACCESS数据库查询工具{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">
                    <i class="fas fa-database me-2"></i>
                    数据库信息
                </h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <h6><i class="fas fa-file-alt me-2"></i>数据库文件:</h6>
                        <p class="text-muted">{{ db_path }}</p>
                    </div>
                    <div class="col-md-4">
                        <h6><i class="fas fa-table me-2"></i>数据表数量:</h6>
                        <p class="text-muted">{{ tables|length }} 个表</p>
                    </div>
                    <div class="col-md-4">
                        <h6><i class="fas fa-chart-bar me-2"></i>数据分析:</h6>
                        <a href="{{ url_for('agv_analytics') }}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-robot me-1"></i>AGV统计分析
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-header bg-success text-white">
                <h4 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    数据表列表
                </h4>
            </div>
            <div class="card-body">
                {% if tables %}
                    <div class="row">
                        {% for table in tables %}
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card h-100 table-card">
                                <div class="card-body text-center">
                                    <i class="fas fa-table fa-3x text-primary mb-3"></i>
                                    <h5 class="card-title">{{ table }}</h5>
                                    <p class="card-text text-muted">点击查看表数据</p>
                                    <div class="btn-group" role="group">
                                        <a href="{{ url_for('view_table', table_name=table) }}" 
                                           class="btn btn-primary">
                                            <i class="fas fa-eye me-1"></i>查看数据
                                        </a>
                                        <button class="btn btn-outline-primary" 
                                                onclick="loadTableInfo('{{ table }}')">
                                            <i class="fas fa-info-circle me-1"></i>表信息
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="alert alert-warning" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        没有找到数据表
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- AGV分析快捷入口 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card shadow-sm border-warning">
            <div class="card-header bg-warning text-dark">
                <div class="row align-items-center">
                    <div class="col">
                        <h4 class="mb-0">
                            <i class="fas fa-chart-line me-2"></i>
                            AGV数据分析
                        </h4>
                        <small>深入分析AGV运行数据，了解设备使用情况</small>
                    </div>
                    <div class="col-auto">
                        <a href="{{ url_for('agv_analytics') }}" class="btn btn-dark">
                            <i class="fas fa-robot me-1"></i>进入分析页面
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3">
                        <i class="fas fa-trophy fa-2x text-warning mb-2"></i>
                        <h6>运行排行榜</h6>
                        <small class="text-muted">AGV运行次数排名</small>
                    </div>
                    <div class="col-md-3">
                        <i class="fas fa-pie-chart fa-2x text-info mb-2"></i>
                        <h6>占比分析</h6>
                        <small class="text-muted">各AGV使用占比</small>
                    </div>
                    <div class="col-md-3">
                        <i class="fas fa-chart-bar fa-2x text-success mb-2"></i>
                        <h6>统计图表</h6>
                        <small class="text-muted">可视化数据展示</small>
                    </div>
                    <div class="col-md-3">
                        <i class="fas fa-table fa-2x text-primary mb-2"></i>
                        <h6>详细数据</h6>
                        <small class="text-muted">完整统计信息</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 快速查询区域 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-header bg-info text-white">
                <h4 class="mb-0">
                    <i class="fas fa-search me-2"></i>
                    快速查询
                </h4>
            </div>
            <div class="card-body">
                <form id="quickQueryForm">
                    <div class="row">
                        <div class="col-md-6">
                            <label for="tableSelect" class="form-label">选择表:</label>
                            <select class="form-select" id="tableSelect" name="table">
                                <option value="">请选择表...</option>
                                {% for table in tables %}
                                <option value="{{ table }}">{{ table }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="limitInput" class="form-label">查询条数:</label>
                            <input type="number" class="form-control" id="limitInput" 
                                   name="limit" value="7" min="1" max="100">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-info w-100">
                                <i class="fas fa-search me-1"></i>查询
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 查询结果区域 -->
<div id="queryResults" class="mt-4" style="display: none;">
    <div class="card shadow-sm">
        <div class="card-header bg-secondary text-white">
            <div class="row align-items-center">
                <div class="col">
                    <h4 class="mb-0">
                        <i class="fas fa-table me-2"></i>
                        查询结果
                    </h4>
                </div>
                <div class="col-auto">
                    <button class="btn btn-light btn-sm" onclick="toggleQuickColumnControls()">
                        <i class="fas fa-columns me-1"></i>字段控制
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body">
            <!-- 快速字段控制面板 -->
            <div id="quickColumnControlPanel" class="mb-3" style="display: none;">
                <div class="alert alert-info">
                    <div class="row align-items-center">
                        <div class="col">
                            <small><i class="fas fa-info-circle me-1"></i>选择要显示的字段：</small>
                        </div>
                        <div class="col-auto">
                            <button class="btn btn-sm btn-outline-primary me-1" onclick="showAllQuickColumns()">全部显示</button>
                            <button class="btn btn-sm btn-outline-secondary" onclick="hideAllQuickColumns()">全部隐藏</button>
                        </div>
                    </div>
                    <div id="quickColumnCheckboxes" class="mt-2">
                        <!-- 字段复选框将在这里动态生成 -->
                    </div>
                </div>
            </div>

            <div id="resultsContent">
                <!-- 查询结果将在这里显示 -->
            </div>
        </div>
    </div>
</div>

<!-- 表信息模态框 -->
<div class="modal fade" id="tableInfoModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-info-circle me-2"></i>
                    表信息
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="tableInfoContent">
                <!-- 表信息将在这里显示 -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 快速查询功能
document.getElementById('quickQueryForm').addEventListener('submit', function(e) {
    e.preventDefault();
    const table = document.getElementById('tableSelect').value;
    const limit = document.getElementById('limitInput').value;

    if (!table) {
        alert('请选择一个表');
        return;
    }

    // 使用AJAX加载数据到当前页面
    loadTableDataQuick(table, limit);
});

// 快速加载表数据
function loadTableDataQuick(tableName, limit) {
    fetch(`/api/table/${tableName}?limit=${limit}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayQuickResults(data);
                document.getElementById('queryResults').style.display = 'block';
                document.getElementById('queryResults').scrollIntoView({ behavior: 'smooth' });
            } else {
                alert('查询失败: ' + data.error);
            }
        })
        .catch(error => {
            alert('请求失败: ' + error);
        });
}

// 显示快速查询结果
function displayQuickResults(data) {
    const tableData = data.data;
    const columns = tableData.columns;
    const rows = tableData.data;

    // 生成字段控制复选框
    generateQuickColumnCheckboxes(columns);

    let html = `
        <div class="table-responsive">
            <table class="table table-striped table-hover mb-0" id="quickDataTable">
                <thead class="table-dark">
                    <tr>
                        <th scope="col" style="width: 60px;" data-column="-1">#</th>
    `;

    columns.forEach((column, index) => {
        html += `<th scope="col" data-column="${index}" class="quick-column-header">${column}</th>`;
    });

    html += `
                    </tr>
                </thead>
                <tbody>
    `;

    rows.forEach((row, rowIndex) => {
        html += `<tr><td class="text-muted" data-column="-1">${rowIndex + 1}</td>`;
        columns.forEach((column, colIndex) => {
            const cellValue = row[column] || '';
            let cellHtml = '';

            if (column === 'EventString') {
                const displayValue = cellValue.length > 150 ? cellValue.substring(0, 150) + '...' : cellValue;
                const escapedValue = JSON.stringify(cellValue);
                cellHtml = `
                    <div class="event-string-cell">
                        <span class="event-string-preview" title="${cellValue.replace(/"/g, '&quot;')}">${displayValue}</span>
                        ${cellValue.length > 150 ? `<button class="btn btn-sm btn-outline-primary ms-1" onclick="showQuickFullText(${escapedValue}, '${column}')" title="查看完整内容"><i class="fas fa-eye"></i> 全文</button>` : ''}
                    </div>
                `;
            } else {
                const displayValue = cellValue.length > 100 ? cellValue.substring(0, 100) + '...' : cellValue;
                cellHtml = `<span class="data-cell" title="${cellValue}">${displayValue}</span>`;
            }

            html += `
                <td data-column="${colIndex}" class="quick-column-cell">
                    ${cellHtml}
                </td>
            `;
        });
        html += '</tr>';
    });

    html += `
                </tbody>
            </table>
        </div>
        <div class="mt-3">
            <small class="text-muted">
                显示 ${rows.length} 条记录，总共 ${data.info.total_count} 条 |
                <a href="/table/${data.table_name}?limit=${rows.length}" class="text-decoration-none">
                    <i class="fas fa-external-link-alt me-1"></i>查看完整页面
                </a>
            </small>
        </div>
    `;

    document.getElementById('resultsContent').innerHTML = html;
}

// 生成快速字段控制复选框
function generateQuickColumnCheckboxes(columns) {
    let html = '<div class="row">';
    columns.forEach((column, index) => {
        html += `
            <div class="col-md-3 col-sm-4 col-6 mb-1">
                <div class="form-check form-check-inline">
                    <input class="form-check-input quick-column-checkbox" type="checkbox"
                           id="quick_col_${index}"
                           data-column="${index}"
                           checked
                           onchange="toggleQuickColumn(${index})">
                    <label class="form-check-label" for="quick_col_${index}">
                        <small>${column}</small>
                    </label>
                </div>
            </div>
        `;
    });
    html += '</div>';
    document.getElementById('quickColumnCheckboxes').innerHTML = html;
}

// 切换快速字段控制面板
function toggleQuickColumnControls() {
    const panel = document.getElementById('quickColumnControlPanel');
    panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
}

// 切换快速查询中的单个字段
function toggleQuickColumn(columnIndex) {
    const checkbox = document.getElementById(`quick_col_${columnIndex}`);
    const isVisible = checkbox.checked;

    // 切换表头
    const headers = document.querySelectorAll(`#quickDataTable th[data-column="${columnIndex}"]`);
    headers.forEach(header => {
        header.style.display = isVisible ? '' : 'none';
    });

    // 切换数据单元格
    const cells = document.querySelectorAll(`#quickDataTable td[data-column="${columnIndex}"]`);
    cells.forEach(cell => {
        cell.style.display = isVisible ? '' : 'none';
    });
}

// 显示所有快速查询字段
function showAllQuickColumns() {
    const checkboxes = document.querySelectorAll('.quick-column-checkbox');
    checkboxes.forEach((checkbox, index) => {
        checkbox.checked = true;
        toggleQuickColumn(index);
    });
}

// 隐藏所有快速查询字段
function hideAllQuickColumns() {
    const checkboxes = document.querySelectorAll('.quick-column-checkbox');
    checkboxes.forEach((checkbox, index) => {
        checkbox.checked = false;
        toggleQuickColumn(index);
    });
}

// 在快速查询中显示完整文本
function showQuickFullText(content, fieldName) {
    const decodedContent = content;

    // 创建模态框
    const modalHtml = `
        <div class="modal fade" id="quickFullTextModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-file-text me-2"></i>
                            ${fieldName} - 完整内容
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="card">
                            <div class="card-body">
                                <pre style="white-space: pre-wrap; word-wrap: break-word; font-family: inherit; font-size: 0.95rem; line-height: 1.5; margin: 0;">${decodedContent}</pre>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-outline-secondary" onclick="copyQuickFullText()">
                            <i class="fas fa-copy me-1"></i>复制内容
                        </button>
                        <button type="button" class="btn btn-primary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-1"></i>关闭
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除已存在的模态框
    const existingModal = document.getElementById('quickFullTextModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加新模态框
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('quickFullTextModal'));
    modal.show();

    // 存储内容供复制使用
    window.quickFullTextContent = decodedContent;
}

// 复制快速查询的完整文本
function copyQuickFullText() {
    const content = window.quickFullTextContent || '';

    if (navigator.clipboard) {
        navigator.clipboard.writeText(content).then(() => {
            showQuickToast('内容已复制到剪贴板', 'success');
        }).catch(() => {
            showQuickToast('复制失败', 'error');
        });
    } else {
        // 降级方案
        const textArea = document.createElement('textarea');
        textArea.value = content;
        document.body.appendChild(textArea);
        textArea.select();
        try {
            document.execCommand('copy');
            showQuickToast('内容已复制到剪贴板', 'success');
        } catch (err) {
            showQuickToast('复制失败', 'error');
        }
        document.body.removeChild(textArea);
    }
}

// 显示快速提示消息
function showQuickToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(toast);

    setTimeout(() => {
        if (toast.parentNode) {
            toast.remove();
        }
    }, 3000);
}

// 加载表信息
function loadTableInfo(tableName) {
    fetch(`/api/table/${tableName}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const info = data.info;
                let content = `
                    <h6>表名: ${tableName}</h6>
                    <p><strong>总记录数:</strong> ${info.total_count}</p>
                    <h6>字段信息:</h6>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>字段名</th>
                                    <th>类型</th>
                                    <th>大小</th>
                                </tr>
                            </thead>
                            <tbody>
                `;
                
                info.columns_info.forEach(col => {
                    content += `
                        <tr>
                            <td>${col.name}</td>
                            <td>${col.type}</td>
                            <td>${col.size}</td>
                        </tr>
                    `;
                });
                
                content += `
                            </tbody>
                        </table>
                    </div>
                `;
                
                document.getElementById('tableInfoContent').innerHTML = content;
                new bootstrap.Modal(document.getElementById('tableInfoModal')).show();
            } else {
                alert('获取表信息失败: ' + data.error);
            }
        })
        .catch(error => {
            alert('请求失败: ' + error);
        });
}
</script>
{% endblock %}
