{% extends "base.html" %}

{% block title %}{{ table_name }} - 表数据查看{% endblock %}

{% block content %}
<!-- 面包屑导航 -->
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{{ url_for('index') }}">首页</a></li>
        <li class="breadcrumb-item active">{{ table_name }}</li>
    </ol>
</nav>

<!-- 表信息卡片 -->
<div class="row">
    <div class="col-12">
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-primary text-white">
                <div class="row align-items-center">
                    <div class="col">
                        <h4 class="mb-0">
                            <i class="fas fa-table me-2"></i>
                            表: {{ table_name }}
                        </h4>
                    </div>
                    <div class="col-auto">
                        <div class="btn-group">
                            <button class="btn btn-light btn-sm" onclick="refreshData()">
                                <i class="fas fa-refresh me-1"></i>刷新
                            </button>
                            <button class="btn btn-light btn-sm" onclick="exportData()">
                                <i class="fas fa-download me-1"></i>导出
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <h6><i class="fas fa-list-ol me-2"></i>显示记录数:</h6>
                        <p class="text-muted">{{ data.total_rows }} / {{ info.total_count }}</p>
                    </div>
                    <div class="col-md-3">
                        <h6><i class="fas fa-columns me-2"></i>字段数量:</h6>
                        <p class="text-muted">{{ data.columns|length }} 个字段</p>
                    </div>
                    <div class="col-md-3">
                        <h6><i class="fas fa-eye me-2"></i>查询限制:</h6>
                        <p class="text-muted">前 {{ limit }} 条</p>
                    </div>
                    <div class="col-md-3">
                        <h6><i class="fas fa-clock me-2"></i>查询时间:</h6>
                        <p class="text-muted" id="queryTime">刚刚</p>
                    </div>
                </div>
                
                <!-- 查询条数调整和字段控制 -->
                <div class="row mt-3">
                    <div class="col-md-6">
                        <form method="GET" class="d-flex align-items-center">
                            <label for="limitSelect" class="form-label me-2 mb-0">显示条数:</label>
                            <select class="form-select form-select-sm me-2" id="limitSelect" name="limit" style="width: auto;">
                                <option value="7" {{ 'selected' if limit == 7 }}>7条</option>
                                <option value="10" {{ 'selected' if limit == 10 }}>10条</option>
                                <option value="20" {{ 'selected' if limit == 20 }}>20条</option>
                                <option value="50" {{ 'selected' if limit == 50 }}>50条</option>
                                <option value="100" {{ 'selected' if limit == 100 }}>100条</option>
                            </select>
                            <button type="submit" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-search me-1"></i>查询
                            </button>
                        </form>
                    </div>
                    <div class="col-md-6 text-end">
                        <div class="btn-group" role="group">
                            <button class="btn btn-sm btn-outline-secondary" onclick="toggleColumnControls()">
                                <i class="fas fa-columns me-1"></i>字段控制
                            </button>
                            <button class="btn btn-sm btn-outline-info" onclick="toggleEventStringMode()" id="eventStringToggle">
                                <i class="fas fa-expand me-1"></i>展开EventString
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 字段显示/隐藏控制面板 -->
                <div id="columnControlPanel" class="mt-3" style="display: none;">
                    <div class="card">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">
                                <i class="fas fa-eye me-2"></i>字段显示控制
                                <button class="btn btn-sm btn-outline-primary ms-2" onclick="showAllColumns()">全部显示</button>
                                <button class="btn btn-sm btn-outline-secondary ms-1" onclick="hideAllColumns()">全部隐藏</button>
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row" id="columnCheckboxes">
                                {% for column in data.columns %}
                                <div class="col-md-3 col-sm-4 col-6 mb-2">
                                    <div class="form-check">
                                        <input class="form-check-input column-checkbox" type="checkbox"
                                               id="col_{{ loop.index0 }}"
                                               data-column="{{ loop.index0 }}"
                                               checked
                                               onchange="toggleColumn({{ loop.index0 }})">
                                        <label class="form-check-label" for="col_{{ loop.index0 }}">
                                            {{ column }}
                                        </label>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 数据表格 -->
<div class="row">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-database me-2"></i>
                    数据内容
                </h5>
            </div>
            <div class="card-body p-0">
                {% if data.data %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover mb-0" id="dataTable">
                            <thead class="table-dark">
                                <tr>
                                    <th scope="col" style="width: 60px;" data-column="-1">#</th>
                                    {% for column in data.columns %}
                                    <th scope="col" data-column="{{ loop.index0 }}" class="column-header{% if column == 'EventString' %} event-string-header{% endif %}">{{ column }}</th>
                                    {% endfor %}
                                </tr>
                            </thead>
                            <tbody>
                                {% for row in data.data %}
                                <tr>
                                    <td class="text-muted" data-column="-1">{{ loop.index }}</td>
                                    {% for column in data.columns %}
                                    <td data-column="{{ loop.index0 }}" class="column-cell">
                                        {% if column == 'EventString' %}
                                            <div class="event-string-cell">
                                                <span class="event-string-preview" title="{{ row[column] }}">
                                                    {{ row[column][:150] }}{% if row[column]|length > 150 %}...{% endif %}
                                                </span>
                                                {% if row[column]|length > 150 %}
                                                <button class="btn btn-sm btn-outline-primary ms-1"
                                                        onclick="showFullText({{ row[column]|tojson }}, '{{ column }}')"
                                                        title="查看完整内容">
                                                    <i class="fas fa-eye"></i> 全文
                                                </button>
                                                {% endif %}
                                            </div>
                                        {% else %}
                                            <span class="data-cell" title="{{ row[column] }}">
                                                {{ row[column][:100] }}{% if row[column]|length > 100 %}...{% endif %}
                                            </span>
                                        {% endif %}
                                    </td>
                                    {% endfor %}
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">没有数据</h5>
                        <p class="text-muted">该表中没有找到任何记录</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 字段信息 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    字段信息
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>字段名</th>
                                <th>数据类型</th>
                                <th>大小</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for col_info in info.columns_info %}
                            <tr>
                                <td>{{ loop.index }}</td>
                                <td><code>{{ col_info.name }}</code></td>
                                <td>{{ col_info.type }}</td>
                                <td>{{ col_info.size }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 完整文本显示模态框 -->
<div class="modal fade" id="fullTextModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-file-text me-2"></i>
                    <span id="fullTextFieldName">字段内容</span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="card">
                    <div class="card-body">
                        <pre id="fullTextContent" class="mb-0" style="white-space: pre-wrap; word-wrap: break-word; font-family: inherit; font-size: 0.95rem; line-height: 1.5;"></pre>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" onclick="copyFullText()">
                    <i class="fas fa-copy me-1"></i>复制内容
                </button>
                <button type="button" class="btn btn-primary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>关闭
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 字段控制功能
function toggleColumnControls() {
    const panel = document.getElementById('columnControlPanel');
    if (panel.style.display === 'none') {
        panel.style.display = 'block';
    } else {
        panel.style.display = 'none';
    }
}

// 切换单个字段显示/隐藏
function toggleColumn(columnIndex) {
    const checkbox = document.getElementById(`col_${columnIndex}`);
    const isVisible = checkbox.checked;

    // 切换表头
    const headers = document.querySelectorAll(`th[data-column="${columnIndex}"]`);
    headers.forEach(header => {
        header.style.display = isVisible ? '' : 'none';
    });

    // 切换数据单元格
    const cells = document.querySelectorAll(`td[data-column="${columnIndex}"]`);
    cells.forEach(cell => {
        cell.style.display = isVisible ? '' : 'none';
    });

    // 保存状态到localStorage
    saveColumnVisibility();
}

// 显示所有字段
function showAllColumns() {
    const checkboxes = document.querySelectorAll('.column-checkbox');
    checkboxes.forEach((checkbox, index) => {
        checkbox.checked = true;
        toggleColumn(index);
    });
}

// 隐藏所有字段
function hideAllColumns() {
    const checkboxes = document.querySelectorAll('.column-checkbox');
    checkboxes.forEach((checkbox, index) => {
        checkbox.checked = false;
        toggleColumn(index);
    });
}

// 保存字段可见性状态
function saveColumnVisibility() {
    const visibility = {};
    const checkboxes = document.querySelectorAll('.column-checkbox');
    checkboxes.forEach(checkbox => {
        const columnIndex = checkbox.getAttribute('data-column');
        visibility[columnIndex] = checkbox.checked;
    });
    localStorage.setItem(`columnVisibility_{{ table_name }}`, JSON.stringify(visibility));
}

// 恢复字段可见性状态
function restoreColumnVisibility() {
    const saved = localStorage.getItem(`columnVisibility_{{ table_name }}`);
    if (saved) {
        try {
            const visibility = JSON.parse(saved);
            Object.keys(visibility).forEach(columnIndex => {
                const checkbox = document.getElementById(`col_${columnIndex}`);
                if (checkbox) {
                    checkbox.checked = visibility[columnIndex];
                    toggleColumn(parseInt(columnIndex));
                }
            });
        } catch (e) {
            console.error('恢复字段可见性状态失败:', e);
        }
    }
}

// EventString显示模式切换
let eventStringExpanded = false;

function toggleEventStringMode() {
    eventStringExpanded = !eventStringExpanded;
    const button = document.getElementById('eventStringToggle');
    const eventStringCells = document.querySelectorAll('.event-string-preview');

    if (eventStringExpanded) {
        // 展开模式：显示完整内容
        button.innerHTML = '<i class="fas fa-compress me-1"></i>收起EventString';
        button.className = 'btn btn-sm btn-warning';

        eventStringCells.forEach(cell => {
            cell.style.maxHeight = 'none';
            cell.style.webkitLineClamp = 'unset';
            cell.style.overflow = 'visible';
        });

        // 隐藏所有展开按钮
        document.querySelectorAll('.event-string-cell .btn').forEach(btn => {
            if (btn.textContent.includes('全文')) {
                btn.style.display = 'none';
            }
        });

    } else {
        // 收起模式：恢复预览显示
        button.innerHTML = '<i class="fas fa-expand me-1"></i>展开EventString';
        button.className = 'btn btn-sm btn-outline-info';

        eventStringCells.forEach(cell => {
            cell.style.maxHeight = '4.5em';
            cell.style.webkitLineClamp = '3';
            cell.style.overflow = 'hidden';
        });

        // 显示展开按钮
        document.querySelectorAll('.event-string-cell .btn').forEach(btn => {
            if (btn.textContent.includes('全文')) {
                btn.style.display = 'inline-block';
            }
        });
    }

    // 保存状态
    localStorage.setItem(`eventStringExpanded_{{ table_name }}`, eventStringExpanded);
}

// 恢复EventString显示状态
function restoreEventStringMode() {
    const saved = localStorage.getItem(`eventStringExpanded_{{ table_name }}`);
    if (saved === 'true') {
        toggleEventStringMode();
    }
}

// 页面加载时恢复状态
document.addEventListener('DOMContentLoaded', function() {
    restoreColumnVisibility();
    restoreEventStringMode();
});

// 显示完整文本内容
function showFullText(content, fieldName) {
    document.getElementById('fullTextFieldName').textContent = fieldName + ' - 完整内容';
    document.getElementById('fullTextContent').textContent = content;

    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('fullTextModal'));
    modal.show();
}

// 复制完整文本内容
function copyFullText() {
    const content = document.getElementById('fullTextContent').textContent;

    if (navigator.clipboard) {
        navigator.clipboard.writeText(content).then(() => {
            showToast('内容已复制到剪贴板', 'success');
        }).catch(() => {
            showToast('复制失败', 'error');
        });
    } else {
        // 降级方案
        const textArea = document.createElement('textarea');
        textArea.value = content;
        document.body.appendChild(textArea);
        textArea.select();
        try {
            document.execCommand('copy');
            showToast('内容已复制到剪贴板', 'success');
        } catch (err) {
            showToast('复制失败', 'error');
        }
        document.body.removeChild(textArea);
    }
}

// 显示提示消息
function showToast(message, type = 'info') {
    // 创建提示元素
    const toast = document.createElement('div');
    toast.className = `alert alert-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(toast);

    // 自动移除
    setTimeout(() => {
        if (toast.parentNode) {
            toast.remove();
        }
    }, 3000);
}

// 刷新数据
function refreshData() {
    location.reload();
}

// 导出数据（简单实现）
function exportData() {
    const table = document.getElementById('dataTable');
    if (!table) {
        alert('没有数据可导出');
        return;
    }
    
    let csv = '';
    const rows = table.querySelectorAll('tr');
    
    for (let i = 0; i < rows.length; i++) {
        const cells = rows[i].querySelectorAll('th, td');
        const rowData = [];
        
        for (let j = 0; j < cells.length; j++) {
            let cellData = cells[j].textContent.trim();
            // 处理包含逗号的数据
            if (cellData.includes(',')) {
                cellData = '"' + cellData + '"';
            }
            rowData.push(cellData);
        }
        
        csv += rowData.join(',') + '\n';
    }
    
    // 创建下载链接
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', '{{ table_name }}_data.csv');
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// 更新查询时间
document.getElementById('queryTime').textContent = new Date().toLocaleString('zh-CN');

// 表格行点击高亮
document.querySelectorAll('#dataTable tbody tr').forEach(row => {
    row.addEventListener('click', function() {
        // 移除其他行的高亮
        document.querySelectorAll('#dataTable tbody tr').forEach(r => {
            r.classList.remove('table-warning');
        });
        // 添加当前行高亮
        this.classList.add('table-warning');
    });
});
</script>
{% endblock %}
